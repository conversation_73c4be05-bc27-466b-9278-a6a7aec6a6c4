#!/usr/bin/env python
"""
Demo script to test the provider architecture
This script demonstrates the key features of the new provider system
"""
import os
import sys
import django

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'videoagent.settings')
django.setup()

from videos.services.providers import ProviderFactory, validate_provider_setup
from videos.constants import STAGE_PROVIDERS, IMAGE_PROVIDER_MAPPING, EXTERNAL_STAGES, INTERNAL_STAGES


def main():
    print("🚀 Provider Architecture Demo")
    print("=" * 50)
    
    # 1. Initialize provider factory
    print("\n1️⃣  Initializing Provider Factory...")
    try:
        ProviderFactory.initialize()
        print("   ✅ Provider factory initialized successfully")
    except Exception as e:
        print(f"   ❌ Failed to initialize: {str(e)}")
        return
    
    # 2. Show stage mappings
    print("\n2️⃣  Stage-Provider Mappings:")
    for stage, provider in STAGE_PROVIDERS.items():
        print(f"   📋 {stage} → {provider}")
    
    # 3. Show image provider mappings
    print("\n3️⃣  Image Provider Mappings:")
    for image_provider, actual_provider in IMAGE_PROVIDER_MAPPING.items():
        print(f"   🖼️  {image_provider} → {actual_provider}")
    
    # 4. Test dynamic provider selection
    print("\n4️⃣  Testing Dynamic Provider Selection:")
    
    # Mock video object for testing
    class MockVideo:
        def __init__(self, image_provider):
            self.id = 123
            self.image_provider = image_provider
    
    test_cases = ['together_ai', 'runpod', 'dalle', 'unknown_provider', None]
    
    for image_provider in test_cases:
        try:
            mock_video = MockVideo(image_provider)
            provider = ProviderFactory.get_provider('image_generation', mock_video)
            print(f"   🔧 {image_provider or 'None'} → {provider.provider_name}")
        except Exception as e:
            print(f"   ❌ {image_provider or 'None'} → Error: {str(e)}")
    
    # 5. Test static provider selection
    print("\n5️⃣  Testing Static Provider Selection:")
    
    static_stages = ['script_generation', 'voice_generation', 'caption_generation', 'video_composition']
    
    for stage in static_stages:
        try:
            provider = ProviderFactory.get_provider(stage)
            print(f"   🔧 {stage} → {provider.provider_name}")
        except Exception as e:
            print(f"   ❌ {stage} → Error: {str(e)}")
    
    # 6. Show internal vs external stages
    print("\n6️⃣  Stage Categories:")
    print(f"   🔧 External Stages: {', '.join(EXTERNAL_STAGES)}")
    print(f"   🏠 Internal Stages: {', '.join(INTERNAL_STAGES)}")
    
    # 7. Validate provider setup
    print("\n7️⃣  Provider Setup Validation:")
    validation_results = validate_provider_setup()
    
    for stage, status in validation_results.items():
        if status == 'valid':
            print(f"   ✅ {stage}: {status}")
        elif 'dynamic' in status or 'internal' in status:
            print(f"   🔄 {stage}: {status}")
        else:
            print(f"   ❌ {stage}: {status}")
    
    # 8. Show available providers
    print("\n8️⃣  Available Providers:")
    try:
        providers_info = ProviderFactory.list_providers()
        for name, info in providers_info.items():
            print(f"   📦 {name}:")
            print(f"      Type: {info.get('type', 'unknown')}")
            print(f"      Stages: {', '.join(info.get('supported_stages', []))}")
    except Exception as e:
        print(f"   ❌ Failed to list providers: {str(e)}")
    
    # 9. Test provider capabilities
    print("\n9️⃣  Provider Capabilities:")
    
    test_providers = ['n8n', 'nca', 'together_ai', 'runpod']
    
    for provider_name in test_providers:
        try:
            provider = ProviderFactory.get_provider_by_name(provider_name)
            if provider:
                print(f"   🔧 {provider_name}:")
                print(f"      Supported stages: {', '.join(provider.get_supported_stages())}")
                print(f"      Provider type: {provider.get_provider_type()}")
                print(f"      Is async: {provider.is_async_provider()}")
                print(f"      Requires callback: {provider.requires_callback()}")
            else:
                print(f"   ❌ {provider_name}: Not available")
        except Exception as e:
            print(f"   ❌ {provider_name}: Error - {str(e)}")
    
    print("\n" + "=" * 50)
    print("✅ Demo completed successfully!")
    print("\nKey Features Demonstrated:")
    print("• Dynamic provider selection for image generation")
    print("• Static provider mapping for other stages")
    print("• Provider factory initialization and management")
    print("• Stage categorization (internal vs external)")
    print("• Provider capability validation")
    print("• Error handling and fallback mechanisms")


if __name__ == "__main__":
    main()
