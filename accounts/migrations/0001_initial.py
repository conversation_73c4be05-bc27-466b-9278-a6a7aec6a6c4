# Generated by Django 5.2.1 on 2025-07-06 11:06

import django.contrib.postgres.fields
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('topic', models.CharField(max_length=255)),
                ('platforms', django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=50), blank=True, default=list, size=None)),
                ('credentials', models.JSONField(blank=True, default=dict)),
                ('language', models.Char<PERSON>ield(default='english', max_length=50)),
                ('status', models.Char<PERSON>ield(choices=[('active', 'Active'), ('inactive', 'Inactive')], default='active', max_length=10)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'db_table': 'accounts',
            },
        ),
    ]
