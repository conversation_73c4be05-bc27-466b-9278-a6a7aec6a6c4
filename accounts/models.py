from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.utils import timezone
from authentication.models import User

class Account(models.Model):
    class Meta:
        db_table = 'accounts'

    STATUS_CHOICES = (
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='accounts')
    name = models.CharField(max_length=255)
    topic = models.CharField(max_length=255)
    platforms = ArrayField(models.CharField(max_length=50), blank=True, default=list)
    credentials = models.JSONField(default=dict, blank=True)
    language = models.CharField(max_length=50, default='english')
    status = models.Char<PERSON>ield(max_length=10, choices=STATUS_CHOICES, default='active')
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.name} - {self.user.email}"
