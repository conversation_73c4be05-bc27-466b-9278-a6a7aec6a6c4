# Video Automation SASS Application

A backend API implementation for a Video Automation SASS application using Django and Python.

## Features

- User Authentication (Email/Password and Google Sign-in)
- OTP verification for email and password reset
- Video task management
- Video editing and processing
- Account management
- Stripe payment integration
- N8N integration for video creation

## Technology Stack

- Python 3.12+
- Django 5.2
- Django REST Framework
- PostgreSQL
- Stripe for payment processing
- Integration with n8n workflow automation

## Setup Instructions

### Prerequisites

- Python 3.12+
- PostgreSQL
- Stripe account
- n8n instance running with video creation workflows

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd VideoAgent
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure environment variables by creating a `.env` file with the following contents (customize as needed):
```
SECRET_KEY=your-secret-key
DEBUG=True
ALLOWED_HOSTS=*,localhost,127.0.0.1
DB_NAME=videoagent
DB_USER=postgres
DB_PASSWORD=your-database-password
DB_HOST=localhost
DB_PORT=5432
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
CORS_ALLOW_ALL_ORIGINS=True
N8N_WEBHOOK_BASE_URL=https://n8n.syncu.in/webhook/
N8N_CREATE_VIDEO_WEBHOOK=ebfebf5c-6990-4deb-940e-c079d6fe9f90/create-video/
GOOGLE_CALLBACK_URL=http://localhost:8000/api/auth/google/callback/
```

5. Run database migrations:
```bash
python manage.py makemigrations
python manage.py migrate
```

6. Create a superuser for the admin interface:
```bash
python manage.py createsuperuser
```

7. Run the development server:
```bash
python manage.py runserver
```

The API should now be accessible at `http://localhost:8000/api/`

## API Endpoints

### Authentication

- `POST /api/auth/register/` - Register a new user
- `POST /api/auth/verify-otp/` - Verify email OTP
- `POST /api/auth/login/` - Login with email and password
- `POST /api/auth/google-login/` - Login with Google
- `POST /api/auth/forgot-password/` - Request password reset
- `POST /api/auth/reset-password/` - Reset password with OTP
- `GET/PUT /api/auth/profile/` - Get or update user profile
- `POST /api/auth/change-password/` - Change user password
- `POST /api/auth/logout/` - Logout

### Accounts

- `GET /api/accounts/` - List all accounts
- `POST /api/accounts/` - Create new account
- `GET /api/accounts/{id}/` - Get account details
- `PUT/PATCH /api/accounts/{id}/` - Update account
- `DELETE /api/accounts/{id}/` - Delete account

### Videos

- `GET /api/videos/tasks/` - List video tasks
- `POST /api/videos/tasks/` - Create video task
- `GET /api/videos/tasks/{id}/` - Get video task details
- `PUT/PATCH /api/videos/tasks/{id}/` - Update video task
- `DELETE /api/videos/tasks/{id}/` - Delete video task
- `GET /api/videos/videos/` - List videos
- `GET /api/videos/videos/{id}/` - Get video details
- `PUT/PATCH /api/videos/videos/{id}/` - Update video
- `POST /api/videos/webhook/n8n-callback/{task_id}/` - n8n callback endpoint

### Payments

- `GET /api/payments/packages/` - List available packages
- `GET /api/payments/user-packages/` - List user's subscribed packages
- `GET /api/payments/payment-history/` - List payment history
- `POST /api/payments/create-checkout-session/` - Create Stripe checkout session
- `POST /api/payments/webhook/` - Stripe webhook endpoint

## Webhook Integrations

### n8n Integration

The application integrates with n8n for video creation workflows. The n8n webhook URL structure is:

```
https://n8n.syncu.in/webhook/ebfebf5c-6990-4deb-940e-c079d6fe9f90/create-video/:taskId
```

n8n should call back to the API with the created video URL at:

```
http://your-api-domain/api/videos/webhook/n8n-callback/{task_id}/
```

### Stripe Integration

The application integrates with Stripe for payment processing. Configure your Stripe webhook to point to:

```
http://your-api-domain/api/payments/webhook/
```

## 🎬 Video Creation Flow

The system supports an 8-stage video creation pipeline with N8N integration:

1. **Script Generation** (`script_generation`) - Generate video script from user context
2. **Voice Generation** (`voice_generation`) - Convert script to speech using TTS
3. **Caption Generation** (`caption_generation`) - Generate captions/subtitles for the video
4. **Image Prompt Generation** (`image_prompt_generation`) - Create prompts for visual content
5. **Image Generation** (`image_generation`) - Generate images using AI providers
6. **Clip Creation** (`clip_creation`) - Create video clips from generated assets
7. **Track Creation** (`track_creation`) - Assemble clips into video tracks
8. **Video Composition** (`video_composition`) - Final video assembly and rendering

### Stage Callback Endpoints

Each stage triggers an N8N workflow and waits for a callback with the results:

- **Script Generation**: `POST /api/videos/callback/recieve-response/script`
- **Voice Generation**: `POST /api/videos/callback/recieve-response/voice`
- **Caption Generation**: `POST /api/videos/callback/recieve-response/caption`
- **Image Prompt Generation**: `POST /api/videos/callback/recieve-response/image_prompt`
- **Image Generation**: `POST /api/videos/callback/recieve-response/image_generation`
- **Clip Creation**: `POST /api/videos/callback/recieve-response/clip`
- **Track Creation**: `POST /api/videos/callback/recieve-response/tracks`
- **Video Composition**: `POST /api/videos/callback/recieve-response/compose`

### Video Creation Process

1. User creates a video task via `POST /api/videos/tasks/save_and_proceed`
2. System creates a video record and starts the first stage (`script_generation`)
3. Each stage calls N8N, which processes and returns results via callback
4. System automatically progresses to the next stage upon successful completion
5. Final stage produces the completed video with `production_url`

## License

[MIT License](LICENSE)