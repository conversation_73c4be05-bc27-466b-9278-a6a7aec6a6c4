"""
Consumer orchestration - run multiple consumers in parallel
"""
import os
import sys
import signal
import subprocess
import time
import logging
from typing import List, Dict

logger = logging.getLogger(__name__)


class ConsumerOrchestrator:
    """Orchestrates multiple Kafka consumers"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.running = True
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\nReceived signal {signum}, shutting down consumers...")
        self.stop_all_consumers()
        sys.exit(0)
    
    def start_consumer(self, consumer_name: str, command: List[str]) -> bool:
        """Start a single consumer process"""
        try:
            print(f"Starting {consumer_name} consumer...")
            
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes.append(process)
            print(f"{consumer_name} consumer started with PID {process.pid}")
            return True
            
        except Exception as e:
            print(f"Failed to start {consumer_name} consumer: {e}")
            return False
    
    def start_all_consumers(self):
        """Start all video creation consumers"""
        consumers = [
            {
                'name': 'Stage Orchestrator',
                'command': ['python', 'manage.py', 'run_stage_orchestrator']
            },
            {
                'name': 'N8N Caller',
                'command': ['python', 'manage.py', 'run_n8n_caller']
            },
            {
                'name': 'Data Processor',
                'command': ['python', 'manage.py', 'run_data_processor']
            }
        ]
        
        print("Starting video creation event consumers...")
        
        for consumer in consumers:
            success = self.start_consumer(consumer['name'], consumer['command'])
            if not success:
                print(f"Failed to start {consumer['name']} consumer")
                self.stop_all_consumers()
                return False
            
            # Brief pause between starts
            time.sleep(1)
        
        print(f"Successfully started {len(self.processes)} consumers")
        return True
    
    def monitor_consumers(self):
        """Monitor consumer processes and restart if needed"""
        print("Monitoring consumer processes...")
        
        while self.running:
            try:
                # Check each process
                for i, process in enumerate(self.processes[:]):
                    if process.poll() is not None:
                        # Process has terminated
                        print(f"Consumer process {process.pid} terminated with code {process.returncode}")
                        
                        # Remove from list
                        self.processes.remove(process)
                        
                        # TODO: Implement restart logic here if needed
                        # For now, just log the termination
                        
                # Wait before next check
                time.sleep(5)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error monitoring consumers: {e}")
                time.sleep(5)
    
    def stop_all_consumers(self):
        """Stop all consumer processes gracefully"""
        print("Stopping all consumer processes...")
        self.running = False
        
        for process in self.processes:
            try:
                print(f"Terminating consumer process {process.pid}...")
                process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process.wait(timeout=10)
                    print(f"Consumer process {process.pid} terminated gracefully")
                except subprocess.TimeoutExpired:
                    print(f"Force killing consumer process {process.pid}...")
                    process.kill()
                    process.wait()
                    
            except Exception as e:
                print(f"Error stopping consumer process {process.pid}: {e}")
        
        self.processes.clear()
        print("All consumer processes stopped")
    
    def run(self):
        """Main orchestrator loop"""
        try:
            if not self.start_all_consumers():
                print("Failed to start consumers")
                return False
            
            print("Video creation event system is running...")
            print("Press Ctrl+C to stop all consumers")
            
            self.monitor_consumers()
            
        except KeyboardInterrupt:
            print("\nReceived interrupt signal")
        except Exception as e:
            print(f"Orchestrator error: {e}")
        finally:
            self.stop_all_consumers()
        
        return True


def main():
    """Main entry point"""
    print("Video Creation Event System - Consumer Orchestrator")
    print("=" * 50)
    
    orchestrator = ConsumerOrchestrator()
    success = orchestrator.run()
    
    if success:
        print("Consumer orchestrator completed successfully")
    else:
        print("Consumer orchestrator failed")
        sys.exit(1)


if __name__ == '__main__':
    main()
