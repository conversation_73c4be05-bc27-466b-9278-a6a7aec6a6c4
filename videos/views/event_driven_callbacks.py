"""
Event-driven callback views for N8N integration
"""
import logging
import uuid
from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404

from ..models import Video
from events.producer import event_publisher

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def event_driven_callback_response(request, stage):
    """
    Event-driven callback webhook to receive responses from N8N for each stage
    
    This endpoint immediately returns success to N8N and processes the callback asynchronously
    
    URL: /api/videos/callback/event-driven/{stage}
    Method: POST
    
    Args:
        stage: The stage that completed (script, voice, caption, etc.)
    """
    try:
        # Immediately extract basic info and validate
        data = request.data
        input_data = data.get('input', {})
        output_data = data.get('output', {})
        video_id = input_data.get('video_id')
        response_status = data.get('status')  # 'success' or 'error'
        
        if not video_id:
            logger.error(f"Callback for stage '{stage}' missing video_id")
            # Still return 200 to N8N to avoid retries
            return Response({
                'status': 'received',
                'message': 'Callback received but video_id is missing',
                'error': 'video_id is required'
            }, status=status.HTTP_200_OK)
        
        # Basic video existence check
        try:
            video = Video.objects.get(id=video_id)
        except Video.DoesNotExist:
            logger.error(f"Video {video_id} not found for callback stage '{stage}'")
            # Still return 200 to N8N
            return Response({
                'status': 'received',
                'message': f'Callback received but video {video_id} not found',
                'error': f'Video {video_id} not found'
            }, status=status.HTTP_200_OK)
        
        # Get correlation ID from video
        correlation_id = getattr(video, 'correlation_id', str(uuid.uuid4()))
        
        logger.info(f"Received callback for video {video_id}, stage: {stage}, status: {response_status}")
        
        # Immediately return success to N8N
        response_data = {
            'status': 'received',
            'message': f'Callback for stage {stage} received successfully',
            'video_id': video.id,
            'stage': stage,
            'processing_status': 'queued'
        }
        
        # Publish callback received event asynchronously (fire and forget)
        try:
            if response_status == 'success':
                # Publish successful callback event
                event_publisher.publish_n8n_callback_received(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    response_data=output_data,
                    execution_id=input_data.get('execution_id')
                )
                
                logger.info(f"Published N8NCallbackReceived event for video {video_id}, stage {stage}")
                
            elif response_status == 'error':
                # Publish stage failed event
                error_message = data.get('error_message', 'Unknown error occurred in N8N')
                event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=error_message
                )
                
                logger.warning(f"Published StageFailed event for video {video_id}, stage {stage}: {error_message}")
                
            else:
                # Invalid status - publish stage failed event
                error_message = f"Invalid callback status: {response_status}. Expected 'success' or 'error'"
                event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=error_message
                )
                
                logger.error(f"Invalid callback status for video {video_id}, stage {stage}: {response_status}")
                
        except Exception as e:
            # Even if event publishing fails, we still return success to N8N
            logger.error(f"Failed to publish callback event for video {video_id}, stage {stage}: {e}")
            response_data['warning'] = 'Event publishing failed but callback was received'
        
        # Always return success to N8N
        return Response(response_data, status=status.HTTP_200_OK)
    
    except Exception as e:
        # Log the error but still return success to N8N to avoid retries
        logger.error(f"Unexpected error processing callback for stage '{stage}': {str(e)}")
        
        return Response({
            'status': 'received',
            'message': f'Callback for stage {stage} received with processing error',
            'error': 'Internal processing error',
            'stage': stage
        }, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def legacy_callback_response(request, stage):
    """
    Legacy callback handler for backward compatibility
    
    This maintains the old synchronous behavior for gradual migration
    """
    try:
        from ..utils.n8n_helpers import handle_video_creation_callback
        from ..services.video_creation_service import video_creation_service
        
        data = request.data
        input_data = data.get('input', {})
        output_data = data.get('output', {})
        video_id = input_data.get('video_id')
        response_status = data.get('status')
        
        if not video_id:
            logger.error(f"Legacy callback for stage '{stage}' missing video_id")
            return Response(
                {'error': 'video_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        video = get_object_or_404(Video, id=video_id)
        
        logger.info(f"Processing legacy callback for video {video_id}, stage: {stage}, status: {response_status}")
        
        if response_status == 'success':
            success = video_creation_service.handle_stage_success(stage, video, output_data)
            
            if success:
                return Response({
                    'status': 'success',
                    'message': f'Stage {stage} completed successfully',
                    'video_id': video.id,
                    'current_stage': video.stage
                })
            else:
                return Response({
                    'status': 'error',
                    'message': f'Failed to proceed after stage {stage}',
                    'video_id': video.id
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        elif response_status == 'error':
            error_message = data.get('error_message', 'Unknown error occurred')
            video_creation_service.handle_stage_error(stage, video, error_message)
            
            return Response({
                'status': 'error',
                'message': f'Stage {stage} failed',
                'error': error_message,
                'video_id': video.id
            })
        
        else:
            logger.error(f"Invalid status '{response_status}' for video {video_id}, stage: {stage}")
            return Response(
                {'error': f'Invalid status: {response_status}. Expected "success" or "error"'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    except Exception as e:
        logger.error(f"Error processing legacy callback for stage '{stage}': {str(e)}")
        return Response(
            {'error': f'Internal server error: {str(e)}'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
