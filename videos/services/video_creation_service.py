"""
Enhanced video creation service with provider architecture
Supports both event-driven external providers and direct internal processing
"""
import logging
import uuid
from typing import Dict, Any, Optional
from django.conf import settings

from ..models import Video, Track, Clip, MediaAsset, MediaGeneration
from ..constants import (
    VIDEO_CREATION_FLOW, EXTERNAL_STAGES, INTERNAL_STAGES,
    STAGE_PROVIDERS, IMAGE_PROVIDER_MAPPING
)
from .providers import ProviderFactory, get_provider_for_stage, ProviderResponse, ProcessingStatus
from events.producer import event_publisher
from events.schemas import EventFactory

logger = logging.getLogger(__name__)


class VideoCreationService:
    """
    Enhanced video creation service with provider architecture
    Handles both event-driven external providers and direct internal processing
    """
    
    def __init__(self):
        # Initialize provider factory
        ProviderFactory.initialize()
    
    def start_video_creation(self, video: Video) -> bool:
        """
        Start the video creation process by publishing initial events
        
        Args:
            video: Video instance
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Generate correlation ID for tracking
            correlation_id = str(uuid.uuid4())
            
            # Set initial status
            video.status = 'in_progress'
            video.stage = 'script_generation'
            video.save()
            
            # Create workflow state
            from events.models import WorkflowState
            WorkflowState.objects.create(
                video_id=video,
                correlation_id=correlation_id,
                current_stage='script_generation'
            )
            
            # Publish video creation started event
            success = event_publisher.publish_video_creation_started(
                video_id=video.id,
                user_id=video.user_id,
                correlation_id=correlation_id,
                task_id=video.task_id if video.task else None,
                account_id=video.account_id if video.account else None,
                video_type=video.video_type
            )
            
            if not success:
                logger.error(f"Failed to publish VideoCreationStarted event for video {video.id}")
                return False
            
            # Publish stage initiation event for first stage
            success = event_publisher.publish_stage_initiated(
                video_id=video.id,
                stage='script_generation',
                correlation_id=correlation_id
            )
            
            if not success:
                logger.error(f"Failed to publish StageInitiated event for video {video.id}")
                return False
            
            logger.info(f"Started enhanced video creation for video {video.id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting video creation for video {video.id}: {str(e)}")
            video.status = 'error'
            video.error = str(e)
            video.save()
            return False
    
    def handle_stage_initiation(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle stage initiation using appropriate provider or internal processing
        
        Args:
            video: Video instance
            stage: The stage to initiate
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Handling stage initiation: {stage} for video {video.id}")
            
            # Validate stage
            if stage not in VIDEO_CREATION_FLOW:
                logger.error(f"Invalid stage: {stage}")
                return False
            
            # Route to appropriate handler
            if stage in INTERNAL_STAGES:
                return self._handle_internal_stage(video, stage, correlation_id)
            else:
                return self._handle_external_stage(video, stage, correlation_id)
                
        except Exception as e:
            logger.error(f"Error handling stage initiation for video {video.id}, stage {stage}: {str(e)}")
            return False
    
    def _handle_external_stage(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle external stage processing using providers
        
        Args:
            video: Video instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Get appropriate provider for this stage
            provider = get_provider_for_stage(stage, video)
            
            # Log provider selection
            logger.info(f"Selected provider '{provider.provider_name}' for stage '{stage}', video {video.id}")
            
            # Process stage with provider
            response = provider.process_stage(video, stage, correlation_id)
            
            # Handle response based on type
            if response.status == ProcessingStatus.SUCCESS:
                # Stage completed successfully
                logger.info(f"Stage '{stage}' completed successfully for video {video.id}")
                
                # Publish stage completion event
                return event_publisher.publish_stage_completed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    stage_data=response.data
                )
                
            elif response.status == ProcessingStatus.PENDING:
                # Async operation initiated
                logger.info(f"Stage '{stage}' initiated async processing for video {video.id}")
                
                # Update video with execution tracking
                if response.execution_id:
                    video.latest_execution_id = response.execution_id
                    video.save()
                
                # For async operations, we wait for callbacks
                return True
                
            else:
                # Stage failed
                logger.error(f"Stage '{stage}' failed for video {video.id}: {response.error_message}")
                
                # Publish stage failure event
                return event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=response.error_message,
                    error_code=response.error_code
                )
                
        except Exception as e:
            logger.error(f"Error in external stage processing for {stage}: {str(e)}")
            
            # Publish stage failure event
            return event_publisher.publish_stage_failed(
                video_id=video.id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=str(e),
                error_code='EXTERNAL_STAGE_ERROR'
            )
    
    def _handle_internal_stage(self, video: Video, stage: str, correlation_id: str) -> bool:
        """
        Handle internal stage processing directly
        
        Args:
            video: Video instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Processing internal stage '{stage}' for video {video.id}")
            
            if stage == 'track_creation':
                result = self._create_tracks(video, correlation_id)
            elif stage == 'clip_creation':
                result = self._create_clips(video, correlation_id)
            else:
                logger.error(f"Unknown internal stage: {stage}")
                return False
            
            if result:
                # Update video stage
                video.stage = stage
                video.save()
                
                # Publish stage completion event
                return event_publisher.publish_stage_completed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    stage_data={'processed_internally': True}
                )
            else:
                # Publish stage failure event
                return event_publisher.publish_stage_failed(
                    video_id=video.id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=f"Internal processing failed for stage {stage}",
                    error_code='INTERNAL_STAGE_ERROR'
                )
                
        except Exception as e:
            logger.error(f"Error in internal stage processing for {stage}: {str(e)}")
            
            # Publish stage failure event
            return event_publisher.publish_stage_failed(
                video_id=video.id,
                stage=stage,
                correlation_id=correlation_id,
                error_message=str(e),
                error_code='INTERNAL_STAGE_ERROR'
            )
    
    def _create_tracks(self, video: Video, correlation_id: str) -> bool:
        """
        Create track records for the video
        Creates video track and audio tracks (speech + bgm if selected)
        
        Args:
            video: Video instance
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Creating tracks for video {video.id}")
            
            # Create video track (layer 0)
            video_track = Track.objects.create(
                video=video,
                type='video',
                layer=0
            )
            logger.info(f"Created video track {video_track.id}")
            
            # Create speech audio track (layer 1)
            speech_track = Track.objects.create(
                video=video,
                type='audio',
                layer=1
            )
            logger.info(f"Created speech audio track {speech_track.id}")
            
            # Create BGM audio track if BGM is selected (layer 2)
            if video.bgm:
                bgm_track = Track.objects.create(
                    video=video,
                    type='audio',
                    layer=2
                )
                logger.info(f"Created BGM audio track {bgm_track.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create tracks for video {video.id}: {str(e)}")
            return False
    
    def _create_clips(self, video: Video, correlation_id: str) -> bool:
        """
        Create clip records for each media asset and assign to appropriate tracks
        
        Args:
            video: Video instance
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Creating clips for video {video.id}")
            
            # Get tracks
            video_track = video.tracks.filter(type='video').first()
            speech_track = video.tracks.filter(type='audio', layer=1).first()
            bgm_track = video.tracks.filter(type='audio', layer=2).first()
            
            if not video_track or not speech_track:
                logger.error(f"Required tracks not found for video {video.id}")
                return False
            
            # Create clips for media assets (images/videos)
            media_assets = MediaAsset.objects.filter(
                generation__video=video,
                type__in=['image', 'video']
            ).order_by('created_at')
            
            current_time = 0.0
            clip_duration = video.duration / len(media_assets) if media_assets else video.duration
            
            for asset in media_assets:
                clip = Clip.objects.create(
                    track=video_track,
                    media=asset,
                    in_point=0.0,
                    out_point=clip_duration,
                    start_time=current_time,
                    opacity=1.0
                )
                logger.info(f"Created media clip {clip.id} for asset {asset.id}")
                current_time += clip_duration
            
            # Create speech clip if speech URL exists
            if video.speech_url:
                speech_clip = Clip.objects.create(
                    track=speech_track,
                    media=None,  # Direct URL, not MediaAsset
                    in_point=0.0,
                    out_point=video.duration,
                    start_time=0.0,
                    opacity=1.0,
                    volume=1.0
                )
                logger.info(f"Created speech clip {speech_clip.id}")
            
            # Create BGM clip if BGM is selected
            if video.bgm and bgm_track:
                bgm_clip = Clip.objects.create(
                    track=bgm_track,
                    media=None,  # BGM file path from video.bgm
                    in_point=0.0,
                    out_point=video.duration,
                    start_time=0.0,
                    opacity=1.0,
                    volume=0.3  # Lower volume for background music
                )
                logger.info(f"Created BGM clip {bgm_clip.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create clips for video {video.id}: {str(e)}")
            return False
    
# Global service instance
video_creation_service = VideoCreationService()
