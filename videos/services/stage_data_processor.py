import logging
from typing import Dict, Any
from django.utils import timezone

from ..models import Video, MediaGeneration, MediaAsset, Track, Clip

logger = logging.getLogger(__name__)


class StageDataProcessor:
    """
    Service to process and store stage-specific data from N8N callbacks
    """
    
    @staticmethod
    def process_script_creation_stage(video: Video, data: Dict[str, Any]):
        """
        Process script generation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Update video with script data
        if 'script' in data:
            video.script = data['script']
        
        if 'title' in data:
            video.title = data['title']
        
        if 'description' in data:
            video.description = data['description']
        
        video.save()
        logger.info(f"Updated script data for video {video.id}")
    
    @staticmethod
    def process_voice_generation_stage(video: Video, data: Dict[str, Any]):
        """
        Process voice generation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Update video with voice/speech data
        if 'speech_url' in data:
            video.speech_url = data['speech_url']
        
        if 'avatar_url' in data:
            video.avatar_url = data['avatar_url']
        
        video.save()
        logger.info(f"Updated voice data for video {video.id}")
    
    @staticmethod
    def process_caption_generation_stage(video: Video, data: Dict[str, Any]):
        """
        Process caption generation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Caption data can be stored in metadata or a separate field
        # For now, we'll log that caption processing is complete
        logger.info(f"Caption generation completed for video {video.id}")
        
        # If there's specific caption data to store, add it here
        if 'caption_data' in data:
            # Could store in video metadata or separate model
            pass
    
    @staticmethod
    def process_image_prompt_generation_stage(video: Video, data: Dict[str, Any]):
        """
        Process image prompt generation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Create MediaGeneration entries for image prompts
        prompts = data.get('image_prompts', [])
        
        for prompt_data in prompts:
            MediaGeneration.objects.create(
                video=video,
                prompt=prompt_data.get('prompt', ''),
                media_type='image',
                media_provider=video.image_provider or 'default'
            )
        
        logger.info(f"Created {len(prompts)} image prompts for video {video.id}")
    
    @staticmethod
    def process_image_generation_stage(video: Video, data: Dict[str, Any]):
        """
        Process image generation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Create MediaAsset entries for generated images
        images = data.get('generated_images', [])
        
        for image_data in images:
            # Find the corresponding MediaGeneration if available
            generation = None
            if 'generation_id' in image_data:
                try:
                    generation = MediaGeneration.objects.get(
                        id=image_data['generation_id'],
                        video=video
                    )
                except MediaGeneration.DoesNotExist:
                    pass
            
            MediaAsset.objects.create(
                generation=generation,
                source_path=image_data.get('image_url', ''),
                type='image',
                duration=0,
                metadata=image_data.get('metadata', {})
            )
        
        logger.info(f"Created {len(images)} image assets for video {video.id}")
    
    @staticmethod
    def process_clip_creation_stage(video: Video, data: Dict[str, Any]):
        """
        Process clip creation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Create or update clips
        clips_data = data.get('clips', [])
        
        for clip_data in clips_data:
            # Find the corresponding track
            track_id = clip_data.get('track_id')
            track = None
            
            if track_id:
                try:
                    track = Track.objects.get(id=track_id, video=video)
                except Track.DoesNotExist:
                    logger.warning(f"Track {track_id} not found for video {video.id}")
                    continue
            else:
                # Create a default track if none specified
                track, created = Track.objects.get_or_create(
                    video=video,
                    type=clip_data.get('track_type', 'video'),
                    layer=clip_data.get('layer', 0)
                )
            
            # Find the corresponding media asset
            media_asset = None
            if 'media_asset_id' in clip_data:
                try:
                    media_asset = MediaAsset.objects.get(
                        id=clip_data['media_asset_id']
                    )
                except MediaAsset.DoesNotExist:
                    pass
            
            Clip.objects.create(
                track=track,
                media=media_asset,
                in_point=clip_data.get('in_point', 0.0),
                out_point=clip_data.get('out_point', 0.0),
                start_time=clip_data.get('start_time', 0.0),
                opacity=clip_data.get('opacity', 1.0),
                volume=clip_data.get('volume', 1.0)
            )
        
        logger.info(f"Created {len(clips_data)} clips for video {video.id}")
    
    @staticmethod
    def process_track_creation_stage(video: Video, data: Dict[str, Any]):
        """
        Process track creation stage data
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Create or update tracks
        tracks_data = data.get('tracks', [])
        
        for track_data in tracks_data:
            Track.objects.get_or_create(
                video=video,
                type=track_data.get('type', 'video'),
                layer=track_data.get('layer', 0)
            )
        
        logger.info(f"Created/updated {len(tracks_data)} tracks for video {video.id}")
    
    @staticmethod
    def process_video_composition_stage(video: Video, data: Dict[str, Any]):
        """
        Process video composition stage data (final stage)
        
        Args:
            video: Video instance
            data: Response data from N8N
        """
        # Update video with final composition data
        if 'production_url' in data:
            video.production_url = data['production_url']
        
        if 'raw_url' in data:
            video.raw_url = data['raw_url']
        
        if 'duration' in data:
            video.duration = data['duration']
        
        if 'thumbnail_url' in data:
            # Could add a thumbnail field to the model
            pass
        
        video.save()
        logger.info(f"Updated composition data for video {video.id}")


# Mapping of stages to their processing functions
STAGE_PROCESSORS = {
    'script_generation': StageDataProcessor.process_script_creation_stage,
    'voice_generation': StageDataProcessor.process_voice_generation_stage,
    'caption_generation': StageDataProcessor.process_caption_generation_stage,
    'image_prompt_generation': StageDataProcessor.process_image_prompt_generation_stage,
    'image_generation': StageDataProcessor.process_image_generation_stage,
    'clip_creation': StageDataProcessor.process_clip_creation_stage,
    'track_creation': StageDataProcessor.process_track_creation_stage,
    'video_composition': StageDataProcessor.process_video_composition_stage,
}
