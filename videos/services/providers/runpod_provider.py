"""
Runpod Provider for image generation
Handles image_generation stage using Runpod API
"""
import logging
import requests
import uuid
import time
from typing import Dict, Any, Optional
from django.conf import settings

from .base import BaseServiceProvider, ProviderResponse, ProcessingStatus, ProviderError

logger = logging.getLogger(__name__)


class RunpodProvider(BaseServiceProvider):
    """
    Provider for Runpod image generation API
    Supports AI-powered image generation for videos using Runpod infrastructure
    """
    
    # Stages supported by Runpod
    SUPPORTED_STAGES = [
        'image_generation'
    ]
    
    def __init__(self):
        super().__init__('runpod')
        self.api_key = getattr(settings, 'RUNPOD_API_KEY', '')
        self.base_url = getattr(settings, 'RUNPOD_BASE_URL', '')
        self.timeout = getattr(settings, 'RUNPOD_REQUEST_TIMEOUT', 300)  # 5 minutes
        self.max_retries = getattr(settings, 'RUNPOD_MAX_RETRIES', 3)
        self.poll_interval = getattr(settings, 'RUNPOD_POLL_INTERVAL', 5)  # seconds
    
    def get_supported_stages(self) -> list:
        """Get list of stages this provider supports"""
        return self.SUPPORTED_STAGES.copy()
    
    def get_provider_type(self) -> str:
        """Get the provider type"""
        return 'http_api'
    
    def process_stage(
        self, 
        video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Process image generation using Runpod API
        
        Args:
            video: Video model instance
            stage: Stage name (should be 'image_generation')
            correlation_id: Correlation ID for tracking
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Response with generated images
        """
        try:
            self.log_operation('process_stage', video.id, stage, {
                'correlation_id': correlation_id
            })
            
            # Validate stage
            if not self.validate_stage(stage):
                raise ProviderError(
                    f"Stage '{stage}' not supported by Runpod provider",
                    self.provider_name,
                    stage,
                    'UNSUPPORTED_STAGE'
                )
            
            # Check API configuration
            if not self.api_key or not self.endpoint_id:
                raise ProviderError(
                    "Runpod API key or endpoint ID not configured",
                    self.provider_name,
                    stage,
                    'MISSING_API_CONFIG'
                )
            
            # Get image prompts from video's media generations
            image_prompts = self._get_image_prompts(video)
            if not image_prompts:
                raise ProviderError(
                    "No image prompts found for video",
                    self.provider_name,
                    stage,
                    'MISSING_IMAGE_PROMPTS'
                )
            
            # Generate images for each prompt
            generated_images = []
            
            for i, prompt in enumerate(image_prompts):
                try:
                    logger.info(f"Generating image {i+1}/{len(image_prompts)} for video {video.id}")
                    
                    # Prepare API payload
                    api_payload = self.prepare_payload(video, stage)
                    api_payload['input']['prompt'] = prompt
                    
                    # Submit job to Runpod
                    job_response = self._submit_job(api_payload)
                    job_id = job_response.get('id')
                    
                    if not job_id:
                        raise ProviderError(
                            "Failed to get job ID from Runpod",
                            self.provider_name,
                            stage,
                            'INVALID_JOB_RESPONSE'
                        )
                    
                    # Poll for completion
                    result_data = self._poll_job_completion(job_id)
                    
                    # Process result
                    image_data = self._process_image_response(result_data, prompt, i)
                    generated_images.append(image_data)
                    
                except Exception as e:
                    logger.error(f"Failed to generate image {i+1}: {str(e)}")
                    # Continue with other images, but log the error
                    generated_images.append({
                        'error': str(e),
                        'prompt': prompt,
                        'index': i
                    })
            
            # Create MediaAssets for generated images
            self._create_media_assets(video, generated_images)
            
            return ProviderResponse.success(
                data={
                    'generated_images': generated_images,
                    'total_images': len(generated_images),
                    'successful_images': len([img for img in generated_images if 'error' not in img])
                },
                metadata={
                    'stage': stage,
                    'provider': 'runpod',
                    'endpoint_id': self.endpoint_id
                }
            )
            
        except ProviderError:
            raise
        except Exception as e:
            logger.error(f"Runpod provider error for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def prepare_payload(self, video, stage: str) -> Dict[str, Any]:
        """
        Prepare API payload for Runpod
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: Runpod API payload
        """
        # Get image generation parameters
        width, height = self._get_image_dimensions(video.orientation)
        
        payload = {
            'input': {
                'prompt': '',  # Will be filled per image
                'width': width,
                'height': height,
                'num_outputs': 1,
                'num_inference_steps': 20,
                'guidance_scale': 7.5,
                'scheduler': 'DPMSolverMultistep',
                'seed': None  # Random seed
            }
        }
        
        # Add video style parameters
        if video.video_style:
            payload['input']['style'] = video.video_style
        
        return payload
    
    def handle_response(
        self, 
        response: Any, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Process Runpod API response
        
        Args:
            response: Raw response from Runpod API
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response
        """
        try:
            if isinstance(response, dict):
                # Check for successful completion
                if response.get('status') == 'COMPLETED' and 'output' in response:
                    return ProviderResponse.success(
                        data=response,
                        metadata={'stage': stage, 'provider': 'runpod'}
                    )
                elif response.get('status') == 'FAILED':
                    return ProviderResponse.failure(
                        error_message=response.get('error', 'Runpod job failed'),
                        error_code='RUNPOD_JOB_FAILED'
                    )
                elif response.get('status') in ['IN_QUEUE', 'IN_PROGRESS']:
                    return ProviderResponse.pending(
                        execution_id=response.get('id')
                    )
                else:
                    return ProviderResponse.failure(
                        error_message="Unexpected response status from Runpod",
                        error_code='UNEXPECTED_STATUS'
                    )
            else:
                return ProviderResponse.failure(
                    error_message="Invalid response format from Runpod API",
                    error_code='INVALID_RESPONSE_FORMAT'
                )
                
        except Exception as e:
            logger.error(f"Error handling Runpod response for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def handle_error(
        self, 
        error: Exception, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle Runpod provider errors
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        error_message = str(error)
        error_code = 'RUNPOD_PROVIDER_ERROR'
        
        # Categorize different types of errors
        if isinstance(error, requests.exceptions.Timeout):
            error_code = 'RUNPOD_TIMEOUT_ERROR'
            error_message = f"Runpod API timeout for stage {stage}"
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_code = 'RUNPOD_CONNECTION_ERROR'
            error_message = f"Failed to connect to Runpod API for stage {stage}"
        elif isinstance(error, requests.exceptions.HTTPError):
            error_code = 'RUNPOD_HTTP_ERROR'
            error_message = f"Runpod API HTTP error for stage {stage}: {error_message}"
        elif isinstance(error, requests.exceptions.RequestException):
            error_code = 'RUNPOD_REQUEST_ERROR'
            error_message = f"Runpod API request failed for stage {stage}: {error_message}"
        elif isinstance(error, ProviderError):
            error_code = error.error_code or 'RUNPOD_PROVIDER_ERROR'
            error_message = error.message
        
        self.log_operation('handle_error', video.id, stage, {
            'error_code': error_code,
            'error_message': error_message
        })
        
        return ProviderResponse.failure(
            error_message=error_message,
            error_code=error_code,
            metadata={
                'provider': self.provider_name,
                'stage': stage,
                'video_id': video.id
            }
        )
    
    def _submit_job(self, payload: Dict[str, Any]) -> dict:
        """
        Submit job to Runpod
        
        Args:
            payload: Job payload
            
        Returns:
            dict: Job submission response
            
        Raises:
            requests.RequestException: If request fails
        """
        try:
            url = f"{self.base_url}/run"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            logger.info(f"Submitting job to Runpod endpoint")
            
            response = requests.post(
                url,
                json=payload,
                headers=headers,
                timeout=30  # Shorter timeout for job submission
            )
            
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Runpod job submission failed: {str(e)}")
            raise
    
    def _poll_job_completion(self, job_id: str) -> dict:
        """
        Poll job until completion
        
        Args:
            job_id: Job ID to poll
            
        Returns:
            dict: Final job result
            
        Raises:
            ProviderError: If job fails or times out
        """
        try:
            url = f"{self.base_url}/status/{job_id}"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            start_time = time.time()
            
            while time.time() - start_time < self.timeout:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()
                
                result = response.json()
                status = result.get('status')
                
                if status == 'COMPLETED':
                    logger.info(f"Runpod job {job_id} completed successfully")
                    return result
                elif status == 'FAILED':
                    error_msg = result.get('error', 'Job failed without error details')
                    raise ProviderError(
                        f"Runpod job failed: {error_msg}",
                        self.provider_name,
                        'image_generation',
                        'RUNPOD_JOB_FAILED'
                    )
                elif status in ['IN_QUEUE', 'IN_PROGRESS']:
                    logger.debug(f"Runpod job {job_id} status: {status}")
                    time.sleep(self.poll_interval)
                else:
                    logger.warning(f"Unknown Runpod job status: {status}")
                    time.sleep(self.poll_interval)
            
            # Timeout reached
            raise ProviderError(
                f"Runpod job {job_id} timed out after {self.timeout} seconds",
                self.provider_name,
                'image_generation',
                'RUNPOD_JOB_TIMEOUT'
            )
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to poll Runpod job {job_id}: {str(e)}")
            raise ProviderError(
                f"Failed to poll job status: {str(e)}",
                self.provider_name,
                'image_generation',
                'RUNPOD_POLL_ERROR'
            )
    
    def _get_image_prompts(self, video) -> list:
        """
        Get image prompts from video's media generations
        
        Args:
            video: Video model instance
            
        Returns:
            list: List of image prompts
        """
        try:
            prompts = []
            
            # Get image prompt generations for this video
            image_generations = video.media_generations.filter(
                media_type='image_prompt'
            ).order_by('created_at')
            
            for generation in image_generations:
                prompts.append(generation.prompt)
            
            return prompts
            
        except Exception as e:
            logger.error(f"Failed to get image prompts for video {video.id}: {str(e)}")
            return []
    
    def _get_image_dimensions(self, orientation: str) -> tuple:
        """
        Get image dimensions based on video orientation
        
        Args:
            orientation: Video orientation (landscape, portrait, square)
            
        Returns:
            tuple: (width, height)
        """
        if orientation == 'portrait':
            return (512, 768)  # 2:3 aspect ratio
        elif orientation == 'square':
            return (512, 512)  # 1:1 aspect ratio
        else:  # landscape or default
            return (768, 512)  # 3:2 aspect ratio
    
    def _process_image_response(self, response_data: dict, prompt: str, index: int) -> dict:
        """
        Process individual image generation response
        
        Args:
            response_data: API response data
            prompt: Original prompt
            index: Image index
            
        Returns:
            dict: Processed image data
        """
        try:
            output = response_data.get('output', [])
            if output and len(output) > 0:
                image_url = output[0]  # Get first generated image
                
                return {
                    'url': image_url,
                    'prompt': prompt,
                    'index': index,
                    'metadata': {
                        'provider': 'runpod',
                        'endpoint_id': self.endpoint_id,
                        'job_id': response_data.get('id')
                    }
                }
            else:
                return {
                    'error': 'No image URLs in response',
                    'prompt': prompt,
                    'index': index
                }
                
        except Exception as e:
            logger.error(f"Failed to process image response: {str(e)}")
            return {
                'error': str(e),
                'prompt': prompt,
                'index': index
            }
    
    def _create_media_assets(self, video, generated_images: list):
        """
        Create MediaAsset records for generated images
        
        Args:
            video: Video model instance
            generated_images: List of generated image data
        """
        try:
            from ...models import MediaGeneration, MediaAsset
            
            # Get or create image generation record
            generation, created = MediaGeneration.objects.get_or_create(
                video=video,
                media_type='image',
                media_provider='runpod',
                defaults={
                    'prompt': f"Generated {len(generated_images)} images for video"
                }
            )
            
            # Create MediaAsset for each successful image
            for image_data in generated_images:
                if 'error' not in image_data and image_data.get('url'):
                    MediaAsset.objects.create(
                        generation=generation,
                        source_path=image_data['url'],
                        type='image',
                        metadata={
                            'prompt': image_data.get('prompt', ''),
                            'index': image_data.get('index', 0),
                            'provider': 'runpod',
                            'endpoint_id': self.endpoint_id,
                            'job_id': image_data.get('metadata', {}).get('job_id')
                        }
                    )
            
            logger.info(f"Created MediaAssets for {len([img for img in generated_images if 'error' not in img])} images")
            
        except Exception as e:
            logger.error(f"Failed to create MediaAssets: {str(e)}")
    
    def check_endpoint_health(self) -> bool:
        """
        Check if Runpod endpoint is healthy
        
        Returns:
            bool: True if endpoint is healthy
        """
        try:
            url = f"{self.base_url}/health"
            
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            health_data = response.json()
            return health_data.get('status') == 'healthy'
            
        except Exception as e:
            logger.error(f"Failed to check Runpod endpoint health: {str(e)}")
            return False
