"""
Base provider abstract class for external service providers
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ProcessingStatus(Enum):
    """Standard processing status across all providers"""
    SUCCESS = "success"
    FAILURE = "failure"
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    TIMEOUT = "timeout"
    RETRY_REQUIRED = "retry_required"


@dataclass
class ProviderResponse:
    """Standardized response format for all providers"""
    status: ProcessingStatus
    data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    execution_id: Optional[str] = None
    is_async: bool = False
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'status': self.status.value,
            'data': self.data,
            'error_message': self.error_message,
            'error_code': self.error_code,
            'execution_id': self.execution_id,
            'is_async': self.is_async,
            'metadata': self.metadata
        }
    
    @classmethod
    def success(
        cls, 
        data: Optional[Dict[str, Any]] = None, 
        execution_id: Optional[str] = None,
        is_async: bool = False,
        metadata: Optional[Dict[str, Any]] = None
    ) -> 'ProviderResponse':
        """Create a success response"""
        return cls(
            status=ProcessingStatus.SUCCESS,
            data=data,
            execution_id=execution_id,
            is_async=is_async,
            metadata=metadata
        )
    
    @classmethod
    def failure(
        cls, 
        error_message: str, 
        error_code: Optional[str] = None,
        execution_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> 'ProviderResponse':
        """Create a failure response"""
        return cls(
            status=ProcessingStatus.FAILURE,
            error_message=error_message,
            error_code=error_code,
            execution_id=execution_id,
            metadata=metadata
        )
    
    @classmethod
    def pending(
        cls, 
        execution_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> 'ProviderResponse':
        """Create a pending response for async operations"""
        return cls(
            status=ProcessingStatus.PENDING,
            execution_id=execution_id,
            is_async=True,
            metadata=metadata
        )


class BaseServiceProvider(ABC):
    """
    Abstract base class for all external service providers
    Defines the unified interface for video processing stages
    """
    
    def __init__(self, provider_name: str):
        self.provider_name = provider_name
        self.logger = logging.getLogger(f"{__name__}.{provider_name}")
    
    @abstractmethod
    def process_stage(
        self, 
        video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Main processing method for a specific stage
        
        Args:
            video: Video model instance
            stage: Stage name (e.g., 'script_generation', 'image_generation')
            correlation_id: Unique ID for tracking this workflow
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Standardized response object
        """
        pass
    
    @abstractmethod
    def prepare_payload(self, video, stage: str) -> Dict[str, Any]:
        """
        Prepare stage-specific payload for the provider
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: Provider-specific payload
        """
        pass
    
    @abstractmethod
    def handle_response(
        self, 
        response: Any, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Process and standardize provider response
        
        Args:
            response: Raw response from provider
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response object
        """
        pass
    
    @abstractmethod
    def handle_error(
        self, 
        error: Exception, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle errors and create standardized error response
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        pass
    
    def validate_stage(self, stage: str) -> bool:
        """
        Validate if this provider supports the given stage
        
        Args:
            stage: Stage name to validate
            
        Returns:
            bool: True if stage is supported
        """
        return stage in self.get_supported_stages()
    
    @abstractmethod
    def get_supported_stages(self) -> list:
        """
        Get list of stages this provider supports
        
        Returns:
            list: List of supported stage names
        """
        pass
    
    def get_provider_info(self) -> Dict[str, Any]:
        """
        Get provider information and capabilities
        
        Returns:
            Dict: Provider information
        """
        return {
            'name': self.provider_name,
            'supported_stages': self.get_supported_stages(),
            'type': self.get_provider_type()
        }
    
    @abstractmethod
    def get_provider_type(self) -> str:
        """
        Get the provider type (webhook, http_api, direct)
        
        Returns:
            str: Provider type
        """
        pass
    
    def log_operation(
        self, 
        operation: str, 
        video_id: int, 
        stage: str, 
        details: Optional[Dict[str, Any]] = None
    ):
        """
        Log provider operations for debugging and monitoring
        
        Args:
            operation: Operation name (e.g., 'process_stage', 'handle_response')
            video_id: Video ID
            stage: Stage name
            details: Optional additional details
        """
        log_data = {
            'provider': self.provider_name,
            'operation': operation,
            'video_id': video_id,
            'stage': stage
        }
        if details:
            log_data.update(details)
        
        self.logger.info(f"Provider operation: {log_data}")
    
    def is_async_provider(self) -> bool:
        """
        Check if this provider uses async processing
        
        Returns:
            bool: True if provider is async
        """
        return self.get_provider_type() in ['webhook']
    
    def requires_callback(self) -> bool:
        """
        Check if this provider requires callback handling
        
        Returns:
            bool: True if provider requires callbacks
        """
        return self.is_async_provider()


class ProviderError(Exception):
    """Custom exception for provider-related errors"""
    
    def __init__(
        self, 
        message: str, 
        provider_name: str, 
        stage: str, 
        error_code: Optional[str] = None
    ):
        self.message = message
        self.provider_name = provider_name
        self.stage = stage
        self.error_code = error_code
        super().__init__(self.message)
    
    def to_response(self) -> ProviderResponse:
        """Convert to standardized provider response"""
        return ProviderResponse.failure(
            error_message=self.message,
            error_code=self.error_code,
            metadata={
                'provider': self.provider_name,
                'stage': self.stage
            }
        )
