"""
NCA Provider for HTTP REST API integration
Handles caption_generation and video_composition stages
"""
import logging
import requests
import uuid
from typing import Dict, Any, Optional
from django.conf import settings

from .base import BaseServiceProvider, ProviderResponse, ProcessingStatus, ProviderError

logger = logging.getLogger(__name__)


class NCAProvider(BaseServiceProvider):
    """
    Provider for NCA HTTP REST API integration
    Supports both sync and async response patterns
    """
    
    # Stages supported by NCA
    SUPPORTED_STAGES = [
        'caption_generation',
        'video_composition'
    ]
    
    # NCA API endpoint mappings
    NCA_ENDPOINTS = {
        'caption_generation': '/v1/media/transcribe',
        'video_composition': '/v1/ffmpeg/compose'
    }
    
    def __init__(self):
        super().__init__('nca')
        self.base_url = getattr(settings, 'NCA_API_BASE_URL', 'https://nca-toolkit.syncu.in')
        self.api_key = getattr(settings, 'NCA_API_KEY', '')
        self.timeout = getattr(settings, 'NCA_REQUEST_TIMEOUT', 60)
    
    def get_supported_stages(self) -> list:
        """Get list of stages this provider supports"""
        return self.SUPPORTED_STAGES.copy()
    
    def get_provider_type(self) -> str:
        """Get the provider type"""
        return 'http_api'
    
    def process_stage(
        self, 
        video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Process a stage using NCA HTTP API
        
        Args:
            video: Video model instance
            stage: Stage name
            correlation_id: Correlation ID for tracking
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Response with result data
        """
        try:
            self.log_operation('process_stage', video.id, stage, {
                'correlation_id': correlation_id
            })
            
            # Validate stage
            if not self.validate_stage(stage):
                raise ProviderError(
                    f"Stage '{stage}' not supported by NCA provider",
                    self.provider_name,
                    stage,
                    'UNSUPPORTED_STAGE'
                )
            
            # Prepare payload
            stage_payload = self.prepare_payload(video, stage)
            
            # Add correlation tracking
            stage_payload.update({
                'correlation_id': correlation_id,
                'video_id': video.id
            })
            
            # Merge with additional payload if provided
            if payload:
                stage_payload.update(payload)
            
            # Get API endpoint
            api_url = self._get_api_url(stage)
            
            # Make API request
            response_data = self._make_api_request(api_url, stage_payload, stage)
            
            # Process response
            result = self.handle_response(response_data, video, stage)
            
            # Update video with stage results
            self._update_video_from_response(video, stage, result.data)
            
            return result
            
        except ProviderError:
            raise
        except Exception as e:
            logger.error(f"NCA provider error for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def prepare_payload(self, video, stage: str) -> Dict[str, Any]:
        """
        Prepare stage-specific payload for NCA API
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: NCA API-specific payload
        """
        payload = {
            'video_id': video.id,
            'stage': stage
        }
        
        if stage == 'caption_generation':
            payload.update({
                'script': video.script,
                'speech_url': video.speech_url,
                'language': video.language,
                'format': 'srt'  # or other supported formats
            })
        elif stage == 'video_composition':
            payload.update({
                'orientation': video.orientation,
                'duration': video.duration,
                'video_style': video.video_style
            })
            
            # Add BGM if selected
            if video.bgm:
                payload['bgm'] = {
                    'file_path': video.bgm.file_path,
                    'volume': 0.3  # Default BGM volume
                }
            
            # Add tracks and clips data
            tracks_data = self._prepare_tracks_data(video)
            if tracks_data:
                payload['tracks'] = tracks_data
        
        return payload
    
    def handle_response(
        self, 
        response: Any, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Process and standardize NCA API response
        
        Args:
            response: Raw response from NCA API
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response
        """
        try:
            if isinstance(response, dict):
                # Check for success indicators
                if response.get('status') == 'success' or response.get('success'):
                    return ProviderResponse.success(
                        data=response,
                        execution_id=response.get('job_id'),
                        is_async=response.get('async', False),
                        metadata={'stage': stage, 'provider': 'nca'}
                    )
                else:
                    error_message = response.get('error', 'NCA processing failed')
                    return ProviderResponse.failure(
                        error_message=error_message,
                        error_code=response.get('error_code', 'NCA_PROCESSING_ERROR'),
                        execution_id=response.get('job_id')
                    )
            else:
                return ProviderResponse.failure(
                    error_message="Invalid response format from NCA API",
                    error_code='INVALID_RESPONSE_FORMAT'
                )
                
        except Exception as e:
            logger.error(f"Error handling NCA response for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def handle_error(
        self, 
        error: Exception, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle NCA provider errors
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        error_message = str(error)
        error_code = 'NCA_PROVIDER_ERROR'
        
        # Categorize different types of errors
        if isinstance(error, requests.exceptions.Timeout):
            error_code = 'NCA_TIMEOUT_ERROR'
            error_message = f"NCA API timeout for stage {stage}"
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_code = 'NCA_CONNECTION_ERROR'
            error_message = f"Failed to connect to NCA API for stage {stage}"
        elif isinstance(error, requests.exceptions.HTTPError):
            error_code = 'NCA_HTTP_ERROR'
            error_message = f"NCA API HTTP error for stage {stage}: {error_message}"
        elif isinstance(error, requests.exceptions.RequestException):
            error_code = 'NCA_REQUEST_ERROR'
            error_message = f"NCA API request failed for stage {stage}: {error_message}"
        elif isinstance(error, ProviderError):
            error_code = error.error_code or 'NCA_PROVIDER_ERROR'
            error_message = error.message
        
        self.log_operation('handle_error', video.id, stage, {
            'error_code': error_code,
            'error_message': error_message
        })
        
        return ProviderResponse.failure(
            error_message=error_message,
            error_code=error_code,
            metadata={
                'provider': self.provider_name,
                'stage': stage,
                'video_id': video.id
            }
        )
    
    def _get_api_url(self, stage: str) -> str:
        """Get NCA API URL for stage"""
        endpoint = self.NCA_ENDPOINTS.get(stage)
        if not endpoint:
            raise ProviderError(
                f"No NCA endpoint found for stage: {stage}",
                self.provider_name,
                stage,
                'MISSING_ENDPOINT'
            )
        return f"{self.base_url}{endpoint}"
    
    def _make_api_request(self, url: str, payload: Dict[str, Any], stage: str) -> dict:
        """
        Make HTTP request to NCA API
        
        Args:
            url: NCA API URL
            payload: Request payload
            stage: Stage name for error context
            
        Returns:
            dict: Response data
            
        Raises:
            requests.RequestException: If request fails
        """
        try:
            logger.info(f"Making NCA API request to {url} for stage {stage}")
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            # Add API key if configured
            if self.api_key:
                headers['x-api-key'] = f"{self.api_key}"
            
            response = requests.post(
                url,
                json=payload,
                timeout=self.timeout,
                headers=headers
            )
            
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"NCA API request failed for stage {stage}: {str(e)}")
            raise
    
    def _prepare_tracks_data(self, video) -> Optional[list]:
        """
        Prepare tracks and clips data for video composition
        
        Args:
            video: Video model instance
            
        Returns:
            list: Tracks data for NCA API
        """
        try:
            tracks_data = []
            
            for track in video.tracks.all():
                track_data = {
                    'id': track.id,
                    'type': track.type,
                    'layer': track.layer,
                    'clips': []
                }
                
                for clip in track.clips.all():
                    clip_data = {
                        'id': clip.id,
                        'media_url': clip.media.source_path if clip.media else None,
                        'in_point': clip.in_point,
                        'out_point': clip.out_point,
                        'start_time': clip.start_time,
                        'opacity': clip.opacity
                    }
                    
                    # Add volume for audio clips
                    if track.type == 'audio' and clip.volume is not None:
                        clip_data['volume'] = clip.volume
                    
                    track_data['clips'].append(clip_data)
                
                tracks_data.append(track_data)
            
            return tracks_data if tracks_data else None
            
        except Exception as e:
            logger.warning(f"Failed to prepare tracks data: {str(e)}")
            return None
    
    def _update_video_from_response(self, video, stage: str, response_data: Optional[Dict[str, Any]]):
        """
        Update video model with response data
        
        Args:
            video: Video model instance
            stage: Stage name
            response_data: Response data from API
        """
        if not response_data:
            return
        
        try:
            if stage == 'caption_generation':
                # Update with caption/subtitle data
                if 'captions' in response_data:
                    # Store captions in video metadata or related model
                    pass
            elif stage == 'video_composition':
                # Update with final video URL
                if 'video_url' in response_data:
                    video.production_url = response_data['video_url']
                if 'duration' in response_data:
                    video.duration = response_data['duration']
            
            video.save()
            
        except Exception as e:
            logger.warning(f"Failed to update video from response: {str(e)}")
    
    def check_job_status(self, job_id: str) -> ProviderResponse:
        """
        Check status of async NCA job
        
        Args:
            job_id: Job ID to check
            
        Returns:
            ProviderResponse: Job status response
        """
        try:
            status_url = f"{self.base_url}/v1/jobs/{job_id}/status"
            
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            if self.api_key:
                headers['Authorization'] = f"Bearer {self.api_key}"
            
            response = requests.get(status_url, headers=headers, timeout=10)
            response.raise_for_status()
            
            status_data = response.json()
            
            if status_data.get('status') == 'completed':
                return ProviderResponse.success(data=status_data)
            elif status_data.get('status') == 'failed':
                return ProviderResponse.failure(
                    error_message=status_data.get('error', 'Job failed'),
                    error_code='NCA_JOB_FAILED'
                )
            else:
                return ProviderResponse.pending(execution_id=job_id)
                
        except Exception as e:
            logger.error(f"Failed to check NCA job status: {str(e)}")
            return ProviderResponse.failure(
                error_message=f"Failed to check job status: {str(e)}",
                error_code='NCA_STATUS_CHECK_ERROR'
            )
