import requests
import json
from django.conf import settings
from django.utils import timezone
from ..models import VideoTask, Video, Track

def handle_video_creation_callback(task_id, video_url, data=None):
    """
    Handle callback from n8n after video creation
    
    Args:
        task_id: ID of the VideoTask
        video_url: URL of the created video
        data: Additional data from n8n
    """
    try:
        task = VideoTask.objects.get(id=task_id)
        
        # Update task status
        task.status = 'done'
        
        # Update task with video URL
        if task.video_type == 'real':
            task.real_video_url = video_url
        elif task.video_type == 'avatar':
            task.avatar_voice_url = video_url
            
        task.save()
        
        # Create video record
        video = Video.objects.create(
            user=task.user,
            task=task,
            video_type=task.video_type,
            speech_type=task.speech_type,
            title=data.get('title', f"Video {task.id}"),
            description=data.get('description', ''),
            script=data.get('script', ''),
            script_type=task.script_type,
            production_url=video_url,
            raw_url=data.get('raw_url', ''),
            tts_provider=task.tts_provider,
            tts_voice=task.tts_voice,
            video_style=task.video_style,
            bgm=task.bgm,
            context=task.context,
            image_provider=task.image_provider,
            video_provider=task.video_provider,
            orientation=task.orientation,
            duration=data.get('duration', 0),
            status='done',
            stage='completed',
            publish_status='pending'
        )
        
        # Create a default video track
        Track.objects.create(
            video=video,
            type='video',
            layer=0
        )
        
        # Create a default audio track
        Track.objects.create(
            video=video,
            type='audio',
            layer=0
        )
        
        return video
    except VideoTask.DoesNotExist:
        return None
    except Exception as e:
        # Log the error and update video status to error if video was created
        print(f"Error handling video creation callback: {str(e)}")
        
        # Try to find and update video status if it was partially created
        try:
            video = Video.objects.filter(task_id=task_id).first()
            if video:
                video.status = 'error'
                video.error = str(e)
                video.save()
        except Exception:
            pass
            
        return None