# Configuration Constants

VIDEO_TYPE_CHOICES = (
    ('faceless', 'Faceless'),
    ('avatar', 'Avatar'),
)

SCRIPT_TYPE_CHOICES = (
    ('from_user_idea', 'From User Idea'),
    ('from_transcript', 'From Transcript'),
)

SPEECH_TYPE_CHOICES = (
    ('tts', 'Text-to-Speech'),
)

VIDEO_STYLE_CHOICES = (
    ('cinematic', 'Cinematic'),
    ('modern', 'Modern'),
    ('classic', 'Classic'),
    ('dynamic', 'Dynamic'),
    ('minimal', 'Minimal'),
)

ORIENTATION_CHOICES = (
    ('landscape', 'Landscape'),
    ('portrait', 'Portrait'),
    ('square', 'Square'),
)

VIDEO_STATUS_CHOICES = (
    ('in_queue', 'In Queue'),
    ('in_progress', 'In Progress'),
    ('waiting_for_review', 'Waiting for Review'),
    ('error', 'Error'),
    ('done', 'Done'),
)

VIDEO_STAGE_CHOICES = (
    ('yet_to_start', 'Yet to Start'),
    ('script_generation', 'Script Generation'),
    ('voice_generation', 'Voice Generation'),
    ('caption_generation', 'Caption Generation'),
    ('image_prompt_generation', 'Image Prompt Generation'),
    ('image_generation', 'Image Generation'),
    ('clip_creation', 'Clip Creation'),
    ('track_creation', 'Track Creation'),
    ('video_composition', 'Video Composition'),
    ('completed', 'Completed'),
)

# Stage sequence for video creation flow
VIDEO_CREATION_FLOW = [
    'script_generation',
    'voice_generation',
    'caption_generation',
    'image_prompt_generation',
    'image_generation',
    'clip_creation',
    'track_creation',
    'video_composition'
]

VIDEO_PUBLISH_STATUS_CHOICES = (
    ('pending', 'Pending'),
    ('processing', 'Processing'),
    ('done', 'Done'),
)

TASK_STATUS_CHOICES = (
    ('draft', 'Draft'),
    ('todo', 'Todo'),
    ('inprogress', 'In Progress'),
    ('done', 'Done'),
)

DURATION_CHOICES = (
    (30, '30 seconds'),
    (60, '60 seconds'),
    (90, '90 seconds'),
    (120, '120 seconds'),
)

# Provider Architecture Constants

# Stage to Provider Mapping
STAGE_PROVIDERS = {
    'script_generation': 'n8n',
    'voice_generation': 'n8n', 
    'caption_generation': 'nca',
    'image_prompt_generation': 'n8n',
    'image_generation': 'dynamic',  # Based on video.image_provider
    'clip_creation': 'internal',
    'track_creation': 'internal',
    'video_composition': 'nca',
}

# Dynamic provider mapping for image_generation
IMAGE_PROVIDER_MAPPING = {
    'together_ai': 'together_ai',
    'runpod': 'runpod',
    'dalle': 'together_ai',  # fallback mapping
    'midjourney': 'runpod',  # fallback mapping
}

# Stage categorization
EXTERNAL_STAGES = [
    'script_generation', 'voice_generation', 'caption_generation',
    'image_prompt_generation', 'image_generation', 'video_composition'
]

INTERNAL_STAGES = ['track_creation', 'clip_creation']

# Provider Types
PROVIDER_TYPES = {
    'n8n': 'webhook',
    'nca': 'http_api',
    'together_ai': 'http_api',
    'runpod': 'http_api',
    'internal': 'direct'
}

