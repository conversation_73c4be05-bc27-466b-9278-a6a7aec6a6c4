from django.db import models
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON>ield
from django.utils import timezone
from django.conf import settings
from authentication.models import User
from accounts.models import Account
from videos.constants import ORIENTATION_CHOICES, SCRIPT_TYPE_CHOICES, SPEECH_TYPE_CHOICES, TASK_STATUS_CHOICES, VIDEO_PUBLISH_STATUS_CHOICES, VIDEO_STAGE_CHOICES, VIDEO_STATUS_CHOICES, VIDEO_TYPE_CHOICES, DURATION_CHOICES


def get_full_url(classObj, url_field_name):
    """Helper method to prefix URLs with STORAGE_BASE_URL"""
    # Get the raw value from the database
    url_field = classObj.__dict__.get(url_field_name)
    
    if not url_field:
        return None
    
    # If URL is already absolute (starts with http/https), return as is
    if url_field.startswith(('http://', 'https://')):
        return url_field
    
    # Otherwise, prefix with STORAGE_BASE_URL
    base_url = getattr(settings, 'STORAGE_BASE_URL', '')
    if base_url and not base_url.endswith('/'):
        base_url += '/'
    
    return f"{base_url}{url_field.lstrip('/')}"

class TTSVoice(models.Model):
    class Meta:         
        db_table = 'tts_voices'
    
    provider_name = models.CharField(max_length=100)
    provider_display_name = models.CharField(max_length=100)
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=100)
    _sample_file = models.URLField(db_column='sample_file', blank=True, null=True, help_text="URL to a sample audio file for this voice")
    language = models.CharField(max_length=50, default='en')
    gender = models.CharField(max_length=10, choices=[('male', 'Male'), ('female', 'Female'), ('neutral', 'Neutral')], default='neutral')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        unique_together = ['provider_name', 'name']
    
    @property
    def sample_file(self):
        """Get sample file URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_sample_file')
    
    @sample_file.setter
    def sample_file(self, value):
        """Set sample file URL (stores raw value in database)"""
        self.__dict__['_sample_file'] = value
    
    def __str__(self):
        return f"{self.provider_display_name} - {self.display_name}"


class BGM(models.Model):
    class Meta:
        db_table = 'bgms'
        
    name = models.CharField(max_length=200)
    display_name = models.CharField(max_length=200)
    _file_path = models.CharField(db_column='file_path', max_length=500)
    duration = models.IntegerField(help_text="Duration in seconds")
    genre = models.CharField(max_length=100, blank=True, null=True)
    mood = models.CharField(max_length=100, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    @property
    def file_path(self):
        """Get file path URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_file_path')
    
    @file_path.setter
    def sample_file(self, value):
        """Set sample file URL (stores raw value in database)"""
        self.__dict__['_file_path'] = value
    
    def __str__(self):
        return self.display_name



class VideoTask(models.Model):
    class Meta:
        db_table = 'video_tasks'

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='video_tasks')
    video_type = models.CharField(max_length=20, choices=VIDEO_TYPE_CHOICES)
    context = models.TextField()
    script_type = models.CharField(max_length=20, choices=SCRIPT_TYPE_CHOICES)
    speech_type = models.CharField(max_length=20, choices=SPEECH_TYPE_CHOICES)
    tts_provider = models.CharField(max_length=50, blank=True, null=True)
    tts_voice = models.ForeignKey(TTSVoice, on_delete=models.SET_NULL, blank=True, null=True, related_name='video_tasks')
    video_style = models.CharField(max_length=100, blank=True, null=True)
    bgm = models.ForeignKey(BGM, on_delete=models.SET_NULL, blank=True, null=True, related_name='video_tasks')
    image_provider = models.CharField(max_length=50, blank=True, null=True)
    video_provider = models.CharField(max_length=50, blank=True, null=True)
    orientation = models.CharField(max_length=20, choices=ORIENTATION_CHOICES, default='landscape')
    _real_video_url = models.URLField(db_column='real_video_url', blank=True, null=True)
    _avatar_voice_url = models.URLField(db_column='avatar_voice_url', blank=True, null=True)
    duration = models.IntegerField(choices=DURATION_CHOICES, default=30)  # in seconds
    status = models.CharField(max_length=20, choices=TASK_STATUS_CHOICES, default='draft')
    publish_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    @property
    def real_video_url(self):
        """Get real video URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_real_video_url')
    
    @real_video_url.setter
    def real_video_url(self, value):
        """Set real video URL (stores raw value in database)"""
        self.__dict__['_real_video_url'] = value
    
    @property
    def avatar_voice_url(self):
        """Get avatar voice URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_avatar_voice_url')
    
    @avatar_voice_url.setter
    def avatar_voice_url(self, value):
        """Set avatar voice URL (stores raw value in database)"""
        self.__dict__['_avatar_voice_url'] = value
    
    def __str__(self):
        return f"{self.user.email} - {self.video_type} - {self.created_at}"


class VideoTaskAccount(models.Model):
    class Meta:
        db_table = 'video_task_accounts'

    task = models.ForeignKey(VideoTask, on_delete=models.CASCADE, related_name='task_accounts')
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='task_accounts')
    
    def __str__(self):
        return f"{self.task.id} - {self.account.name}"


class Video(models.Model):
    class Meta:
        db_table = 'videos'
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='videos')
    task = models.ForeignKey(VideoTask, on_delete=models.SET_NULL, related_name='videos', null=True)
    account = models.ForeignKey(Account, on_delete=models.SET_NULL, related_name='videos', null=True)
    video_type = models.CharField(max_length=20, choices=VIDEO_TYPE_CHOICES)
    speech_type = models.CharField(max_length=20, choices=SPEECH_TYPE_CHOICES)
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    script = models.TextField(blank=True, null=True)
    script_type = models.CharField(max_length=20, choices=SCRIPT_TYPE_CHOICES)
    _production_url = models.URLField(db_column='production_url', blank=True, null=True)
    _raw_url = models.URLField(db_column='raw_url', blank=True, null=True)
    _avatar_url = models.URLField(db_column='avatar_url', blank=True, null=True)
    _speech_url = models.URLField(db_column='speech_url', blank=True, null=True)
    tts_provider = models.CharField(max_length=50, blank=True, null=True)
    tts_voice = models.ForeignKey(TTSVoice, on_delete=models.SET_NULL, blank=True, null=True, related_name='videos')
    video_style = models.CharField(max_length=100, default='default_style')
    bgm = models.ForeignKey(BGM, on_delete=models.SET_NULL, blank=True, null=True, related_name='videos')
    context = models.TextField(default='')
    image_provider = models.CharField(max_length=50, blank=True, null=True)
    video_provider = models.CharField(max_length=50, blank=True, null=True)
    language = models.CharField(max_length=50, default='english')
    orientation = models.CharField(max_length=20, choices=ORIENTATION_CHOICES, default='landscape')
    platforms = ArrayField(models.CharField(max_length=50), blank=True, default=list)
    duration = models.IntegerField(default=0)  # in seconds
    status = models.CharField(max_length=20, choices=VIDEO_STATUS_CHOICES, null=True)
    stage = models.CharField(max_length=30, choices=VIDEO_STAGE_CHOICES, default='yet_to_start')
    latest_execution_id = models.CharField(max_length=255, blank=True, null=True, help_text="Latest N8N execution ID")
    error = models.TextField(blank=True, null=True)
    publish_status = models.CharField(max_length=20, choices=VIDEO_PUBLISH_STATUS_CHOICES, default='pending', blank=True, null=True)
    publish_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    @property
    def production_url(self):
        """Get production URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_production_url')
    
    @production_url.setter
    def production_url(self, value):
        """Set production URL (stores raw value in database)"""
        self.__dict__['_production_url'] = value
    
    @property
    def raw_url(self):
        """Get raw URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_raw_url')
    
    @raw_url.setter
    def raw_url(self, value):
        """Set raw URL (stores raw value in database)"""
        self.__dict__['_raw_url'] = value
    
    @property
    def avatar_url(self):
        """Get avatar URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_avatar_url')
    
    @avatar_url.setter
    def avatar_url(self, value):
        """Set avatar URL (stores raw value in database)"""
        self.__dict__['_avatar_url'] = value
    
    @property
    def speech_url(self):
        """Get speech URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_speech_url')
    
    @speech_url.setter
    def speech_url(self, value):
        """Set speech URL (stores raw value in database)"""
        self.__dict__['_speech_url'] = value
    
    def __str__(self):
        return self.title


class Track(models.Model):
    class Meta:
        db_table = 'tracks'

    TYPE_CHOICES = (
        ('video', 'Video'),
        ('audio', 'Audio'),
    )
    
    video = models.ForeignKey(Video, on_delete=models.CASCADE, related_name='tracks')
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    layer = models.IntegerField()
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.video.title} - {self.type} - Layer {self.layer}"


class MediaGeneration(models.Model):
    class Meta:
        db_table = 'media_generations'

    video = models.ForeignKey(Video, on_delete=models.CASCADE, related_name='media_generations')
    prompt = models.TextField()
    media_type = models.CharField(max_length=50)
    media_provider = models.CharField(max_length=50)
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.video.title} - {self.media_type} - {self.created_at}"


class MediaAsset(models.Model):
    class Meta:
        db_table = 'media_assets'

    TYPE_CHOICES = (
        ('video', 'Video'),
        ('image', 'Image'),
        ('audio', 'Audio'),
    )
    
    generation = models.ForeignKey(MediaGeneration, on_delete=models.CASCADE, related_name='media_assets', null=True)
    _source_path = models.URLField(db_column='source_path')
    type = models.CharField(max_length=10, choices=TYPE_CHOICES)
    duration = models.IntegerField(default=0)  # in seconds, if applicable
    metadata = models.JSONField(default=dict, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    @property
    def source_path(self):
        """Get source path URL with STORAGE_BASE_URL prefix"""
        return get_full_url(self, '_source_path')

    @source_path.setter
    def source_path(self, value):
        """Set source path URL (stores raw value in database)"""
        self.__dict__['_source_path'] = value
    
    def __str__(self):
        return f"{self.type} - {self.source_path}"


class Clip(models.Model):
    class Meta:
        db_table = 'clips'

    track = models.ForeignKey(Track, on_delete=models.CASCADE, related_name='clips')
    media = models.ForeignKey(MediaAsset, on_delete=models.SET_NULL, related_name='clips', null=True)
    in_point = models.FloatField(default=0.0)  # in seconds
    out_point = models.FloatField(default=0.0)  # in seconds
    start_time = models.FloatField(default=0.0)  # in seconds
    opacity = models.FloatField(default=1.0)
    volume = models.FloatField(default=1.0, null=True, blank=True)  # only for audio tracks
    created_at = models.DateTimeField(default=timezone.now)
    
    def __str__(self):
        return f"{self.track.video.title} - {self.track.type} - {self.start_time}"
