#!/usr/bin/env python
"""
Test script for video creation flow
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/api"
TEST_USER_TOKEN = "your_test_token_here"  # Replace with actual token

# Headers for authenticated requests
HEADERS = {
    "Authorization": f"Token {TEST_USER_TOKEN}",
    "Content-Type": "application/json"
}

def test_video_creation_flow():
    """Test the complete video creation flow"""
    
    print("🎬 Testing Video Creation Flow")
    print("=" * 50)
    
    # Test data
    test_data = {
        "video_type": "faceless",
        "script_type": "from_user_idea", 
        "speech_type": "tts",
        "context": "Create an educational video about the benefits of renewable energy sources like solar and wind power",
        "tts_provider": "openai",
        "tts_voice": "alloy",
        "video_style": "modern",
        "bgm": "upbeat_tech",
        "image_provider": "dalle",
        "orientation": "landscape",
        "duration": 60
    }
    
    # Step 1: Create video task and start creation
    print("1️⃣ Creating video task...")
    
    response = requests.post(
        f"{BASE_URL}/videos/tasks/save_and_proceed",
        headers=HEADERS,
        json=test_data
    )
    
    if response.status_code in [200, 201]:
        result = response.json()
        task_id = result.get('task_id')
        video_id = result.get('video_id')
        
        print(f"✅ Task created successfully!")
        print(f"   Task ID: {task_id}")
        print(f"   Video ID: {video_id}")
        print(f"   Status: {result.get('task_status')}")
        print(f"   Stage: {result.get('video_stage')}")
        
        # Step 2: Monitor video progress
        print("\n2️⃣ Monitoring video progress...")
        return monitor_video_progress(video_id)
        
    else:
        print(f"❌ Failed to create task: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def monitor_video_progress(video_id):
    """Monitor video creation progress"""
    
    max_attempts = 30  # Maximum monitoring attempts
    attempt = 0
    
    while attempt < max_attempts:
        try:
            response = requests.get(
                f"{BASE_URL}/videos/videos/{video_id}/status",
                headers=HEADERS
            )
            
            if response.status_code == 200:
                status_data = response.json()
                
                print(f"\n📊 Progress Update (Attempt {attempt + 1})")
                print(f"   Status: {status_data.get('status')}")
                print(f"   Stage: {status_data.get('current_stage')} → {status_data.get('next_stage', 'Complete')}")
                print(f"   Progress: {status_data.get('progress_percentage')}%")
                print(f"   Execution ID: {status_data.get('latest_execution_id')}")
                
                if status_data.get('error'):
                    print(f"   ❌ Error: {status_data.get('error')}")
                    return False
                
                if status_data.get('status') == 'done':
                    print(f"\n🎉 Video creation completed!")
                    print(f"   Final stage: {status_data.get('current_stage')}")
                    return True
                
                if status_data.get('status') == 'error':
                    print(f"\n❌ Video creation failed!")
                    print(f"   Error: {status_data.get('error')}")
                    return False
                
            else:
                print(f"❌ Failed to get status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error monitoring progress: {str(e)}")
        
        attempt += 1
        time.sleep(10)  # Wait 10 seconds between checks
    
    print(f"\n⏰ Monitoring timeout after {max_attempts} attempts")
    return False

def test_callback_endpoint():
    """Test the callback endpoint with sample data"""
    
    print("\n🔗 Testing Callback Endpoint")
    print("=" * 50)
    
    # Sample callback data
    callback_data = {
        "video_id": 1,  # Replace with actual video ID
        "status": "success",
        "stage": "script",
        "execution_id": "test_exec_123",
        "script": "This is a test script generated by the system...",
        "title": "Test Video Title",
        "description": "Test video description"
    }
    
    response = requests.post(
        f"{BASE_URL}/videos/callback/recieve-response/script",
        json=callback_data
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Callback test successful!")
        print(f"   Response: {result}")
        return True
    else:
        print(f"❌ Callback test failed: {response.status_code}")
        print(f"   Error: {response.text}")
        return False

def list_videos():
    """List current videos"""
    
    print("\n📋 Current Videos")
    print("=" * 50)
    
    response = requests.get(
        f"{BASE_URL}/videos/videos",
        headers=HEADERS
    )
    
    if response.status_code == 200:
        videos = response.json()
        
        if videos.get('results'):
            for video in videos['results']:
                print(f"Video {video.get('id')}: {video.get('title')}")
                print(f"   Status: {video.get('status')} | Stage: {video.get('stage')}")
                print(f"   Created: {video.get('created_at')}")
                print()
        else:
            print("No videos found.")
    else:
        print(f"❌ Failed to list videos: {response.status_code}")

if __name__ == "__main__":
    print(f"🚀 Video Creation Flow Test")
    print(f"Time: {datetime.now()}")
    print(f"Base URL: {BASE_URL}")
    print()
    
    # Uncomment the test you want to run:
    
    # Full flow test (requires authentication)
    # test_video_creation_flow()
    
    # Callback endpoint test
    # test_callback_endpoint()
    
    # List videos (requires authentication)
    # list_videos()
    
    print("\n📝 Note: Update TEST_USER_TOKEN and BASE_URL before running tests")
    print("💡 Uncomment the test functions you want to run")
