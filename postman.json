{"info": {"name": "VideoAgent API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Postman collection for Video Automation SASS backend with N8N integration", "version": "2.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "Base URL for the API"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/register", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"name\": \"Test User\",\n  \"password\": \"Pass@1234\",\n  \"confirm_password\": \"Pass@1234\",\n  \"mobile\": \"1234567890\"\n}"}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/verify-otp", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/auth/login", "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Pass@1234\"\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "pm.environment.set('token', json.token);"]}}]}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/profile"}}, {"name": "Change Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/change-password", "body": {"mode": "raw", "raw": "{\n  \"old_password\": \"Pass@1234\",\n  \"new_password\": \"NewPass@1234\",\n  \"confirm_password\": \"NewPass@1234\"\n}"}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/auth/logout"}}]}, {"name": "Accounts", "item": [{"name": "List Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts"}}, {"name": "Create Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyC<PERSON><PERSON>\",\n  \"topic\": \"Tech Reviews\",\n  \"platforms\": [\"YouTube\",\"TikTok\"],\n  \"credentials\": {\"api_key\":\"abc123\"},\n  \"language\": \"english\",\n  \"status\": \"active\"\n}"}}}, {"name": "Get Account", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}, {"name": "Update Account", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1", "body": {"mode": "raw", "raw": "{\n  \"name\": \"MyUpdatedChannel\"\n}"}}}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/accounts/1"}}]}, {"name": "Video Tasks", "item": [{"name": "List Video Tasks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks"}}, {"name": "Save Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an educational video about renewable energy sources like solar and wind power. Explain their benefits and environmental impact.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"alloy\",\n  \"video_style\": \"modern\",\n  \"bgm\": \"upbeat_corporate\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 60\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('draft_task_id', json.task_id);", "}"]}}]}, {"name": "Update Draft Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_draft", "body": {"mode": "raw", "raw": "{\n  \"id\": {{draft_task_id}},\n  \"video_type\": \"avatar\",\n  \"context\": \"Updated: Create a comprehensive guide about artificial intelligence and machine learning technologies\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"rachel\",\n  \"video_style\": \"cinematic\",\n  \"bgm\": \"calm_piano\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"portrait\",\n  \"duration\": 90\n}"}}}, {"name": "Save and Proceed Task (Start Video Creation)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"video_type\": \"faceless\",\n  \"context\": \"Create an engaging video about the future of electric vehicles and sustainable transportation. Cover battery technology, charging infrastructure, and environmental benefits.\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"openai\",\n  \"tts_voice\": \"nova\",\n  \"video_style\": \"dynamic\",\n  \"bgm\": \"electronic_beat\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"landscape\",\n  \"duration\": 120\n}"}}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "if (json.task_id) {", "    pm.environment.set('production_task_id', json.task_id);", "}", "if (json.video_id) {", "    pm.environment.set('active_video_id', json.video_id);", "}"]}}]}, {"name": "Update and Proceed Task", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/save_and_proceed", "body": {"mode": "raw", "raw": "{\n  \"id\": {{draft_task_id}},\n  \"video_type\": \"avatar\",\n  \"context\": \"Updated and ready for production: Digital transformation in modern businesses\",\n  \"script_type\": \"from_user_idea\",\n  \"speech_type\": \"tts\",\n  \"tts_provider\": \"elevenlabs\",\n  \"tts_voice\": \"drew\",\n  \"video_style\": \"minimal\",\n  \"bgm\": \"acoustic_folk\",\n  \"image_provider\": \"dalle\",\n  \"video_provider\": \"default\",\n  \"orientation\": \"square\",\n  \"duration\": 60\n}"}}}, {"name": "Get Video Task", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}, {"name": "Delete Video Task", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tasks/1"}}]}, {"name": "Videos", "item": [{"name": "List Videos", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos"}}, {"name": "Get Video Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}"}}, {"name": "Get Video Status (New)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}/status", "description": "Get detailed video creation progress including stage, progress percentage, and estimated completion time"}, "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["var json = pm.response.json();", "console.log('Video Status:', JSON.stringify(json, null, 2));"]}}]}, {"name": "Update Video", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/videos/{{active_video_id}}", "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Video Title\",\n  \"description\": \"Updated video description with more details\"\n}"}}}]}, {"name": "Video Creation Flow - N8N Callbacks", "description": "Endpoints for N8N to send callbacks during video creation stages", "item": [{"name": "Generic Stage Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/script", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"stage\": \"script\",\n  \"execution_id\": \"exec_12345\",\n  \"script\": \"Welcome to our comprehensive guide on renewable energy. In this video, we'll explore the revolutionary impact of solar and wind power technologies...\",\n  \"title\": \"Renewable Energy: Powering the Future\",\n  \"description\": \"An in-depth look at how renewable energy sources are transforming our world and creating a sustainable future for generations to come.\"\n}", "description": "Callback for script generation stage completion"}}}, {"name": "Script Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/script", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_script_creation_001\",\n  \"script\": \"Renewable energy is revolutionizing how we power our world. Solar panels convert sunlight into electricity with incredible efficiency, while wind turbines harness natural air currents to generate clean power. These technologies are not just environmentally friendly – they're also becoming more cost-effective than traditional fossil fuels, making them the smart choice for our energy future.\",\n  \"title\": \"The Renewable Energy Revolution\",\n  \"description\": \"Discover how solar and wind power are transforming our energy landscape and creating a sustainable future.\"\n}"}}}, {"name": "Voice Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/voice", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_voice_generation_001\",\n  \"speech_url\": \"https://storage.example.com/audio/voice_{{active_video_id}}.mp3\",\n  \"avatar_url\": \"https://storage.example.com/avatars/avatar_{{active_video_id}}.mp4\"\n}"}}}, {"name": "Caption Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/caption", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_caption_generation_001\",\n  \"caption_data\": {\n    \"subtitle_url\": \"https://storage.example.com/subtitles/video_{{active_video_id}}.srt\",\n    \"timing_data\": [\n      {\"start\": 0, \"end\": 3, \"text\": \"Renewable energy is revolutionizing\"},\n      {\"start\": 3, \"end\": 6, \"text\": \"how we power our world.\"}\n    ]\n  }\n}"}}}, {"name": "Image Prompt Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/image_prompt", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_image_prompt_generation_001\",\n  \"image_prompts\": [\n    {\n      \"prompt\": \"Modern solar panel installation on residential rooftop with blue sky background, cinematic lighting, high resolution\",\n      \"scene_timing\": {\"start\": 0, \"duration\": 5}\n    },\n    {\n      \"prompt\": \"Wind turbines in a field during golden hour, rotating blades, sustainable energy concept, professional photography\",\n      \"scene_timing\": {\"start\": 5, \"duration\": 5}\n    },\n    {\n      \"prompt\": \"Electric vehicle charging station with green energy symbols, modern design, eco-friendly technology\",\n      \"scene_timing\": {\"start\": 10, \"duration\": 5}\n    }\n  ]\n}"}}}, {"name": "Image Generation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/image_generation", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_image_generation_001\",\n  \"generated_images\": [\n    {\n      \"image_url\": \"https://storage.example.com/images/solar_panels_001.jpg\",\n      \"prompt_id\": 1,\n      \"metadata\": {\n        \"resolution\": \"1920x1080\",\n        \"format\": \"jpg\",\n        \"size\": \"2.3MB\"\n      }\n    },\n    {\n      \"image_url\": \"https://storage.example.com/images/wind_turbines_001.jpg\",\n      \"prompt_id\": 2,\n      \"metadata\": {\n        \"resolution\": \"1920x1080\",\n        \"format\": \"jpg\",\n        \"size\": \"1.8MB\"\n      }\n    },\n    {\n      \"image_url\": \"https://storage.example.com/images/ev_charging_001.jpg\",\n      \"prompt_id\": 3,\n      \"metadata\": {\n        \"resolution\": \"1920x1080\",\n        \"format\": \"jpg\",\n        \"size\": \"2.1MB\"\n      }\n    }\n  ]\n}"}}}, {"name": "Clip Creation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/clip", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_clip_creation_001\",\n  \"clips\": [\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 1,\n      \"start_time\": 0.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    },\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 2,\n      \"start_time\": 5.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    },\n    {\n      \"track_type\": \"video\",\n      \"layer\": 0,\n      \"media_asset_id\": 3,\n      \"start_time\": 10.0,\n      \"in_point\": 0.0,\n      \"out_point\": 5.0,\n      \"opacity\": 1.0\n    }\n  ]\n}"}}}, {"name": "Track Creation Stage Success Callback", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/tracks", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_track_creation_001\",\n  \"tracks\": [\n    {\n      \"type\": \"video\",\n      \"layer\": 0\n    },\n    {\n      \"type\": \"audio\",\n      \"layer\": 0\n    },\n    {\n      \"type\": \"audio\",\n      \"layer\": 1\n    }\n  ]\n}"}}}, {"name": "Video Composition Stage Success Callback (Final)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/compose", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"success\",\n  \"execution_id\": \"exec_video_composition_001\",\n  \"production_url\": \"https://cdn.example.com/videos/final_{{active_video_id}}.mp4\",\n  \"raw_url\": \"https://storage.example.com/raw/video_{{active_video_id}}_raw.mp4\",\n  \"duration\": 120,\n  \"thumbnail_url\": \"https://cdn.example.com/thumbnails/video_{{active_video_id}}.jpg\",\n  \"metadata\": {\n    \"resolution\": \"1920x1080\",\n    \"fps\": 30,\n    \"bitrate\": \"5000kbps\",\n    \"format\": \"mp4\",\n    \"file_size\": \"45.2MB\"\n  }\n}"}}}, {"name": "Error Callback Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/callback/recieve-response/voice", "body": {"mode": "raw", "raw": "{\n  \"video_id\": {{active_video_id}},\n  \"status\": \"error\",\n  \"execution_id\": \"exec_voice_generation_001\",\n  \"error_message\": \"TTS service temporarily unavailable. Please try again later.\"\n}"}}}]}, {"name": "Media Management", "item": [{"name": "List Tracks", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/tracks"}}, {"name": "List Media Generations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-generations"}}, {"name": "List Media Assets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/media-assets"}}, {"name": "List Clips", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/clips"}}]}, {"name": "Legacy Webhooks", "item": [{"name": "n8n Video Callback (Legacy)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": "{{base_url}}/videos/webhook/n8n-callback/1", "body": {"mode": "raw", "raw": "{\n  \"video_url\": \"https://cdn.example.com/video.mp4\",\n  \"title\": \"Auto Generated Video\",\n  \"duration\": 120\n}"}}}]}, {"name": "Configurations", "item": [{"name": "Get Configuration Options", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/videos/configurations", "description": "Get all available options for video creation including TTS voices, video styles, orientations, etc."}}]}, {"name": "Payments", "item": [{"name": "List Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/packages"}}, {"name": "Create Checkout Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/create-checkout-session", "body": {"mode": "raw", "raw": "{\n  \"package_id\": 1,\n  \"success_url\": \"http://localhost:3000/success\",\n  \"cancel_url\": \"http://localhost:3000/cancel\"\n}"}}}, {"name": "List User Packages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/user-packages"}}, {"name": "List Payment History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Token {{token}}"}], "url": "{{base_url}}/payments/payment-history"}}]}]}