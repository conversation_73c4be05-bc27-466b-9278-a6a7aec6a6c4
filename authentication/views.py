from django.shortcuts import render
from rest_framework import status, generics, permissions
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from django.contrib.auth import authenticate, login, logout
from django.utils import timezone
from django.conf import settings
from django.core.mail import send_mail
from datetime import timedelta
import random
import string
from allauth.socialaccount.providers.google.views import GoogleOAuth2Adapter
from allauth.socialaccount.providers.oauth2.client import OAuth2Client
from dj_rest_auth.registration.views import SocialLoginView

from .models import User, OTP
from .serializers import (
    UserSerializer, UserProfileSerializer, ChangePasswordSerializer,
    RegisterSerializer, OTPVerificationSerializer, ForgotPasswordSerializer,
    ResetPasswordSerializer, GoogleLoginSerializer
)


class LoginView(ObtainAuthToken):
    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        password = request.data.get('password')
        
        user = authenticate(request, username=email, password=password)
        
        if user is not None:
            # Ensure email verification
            if not user.is_active:
                # generate OTP for verification
                otp_code = ''.join(random.choices(string.digits, k=6))
                expires_at = timezone.now() + timedelta(minutes=10)
                OTP.objects.create(
                    user=user,
                    code=otp_code,
                    purpose='registration',
                    expires_at=expires_at
                )
                if not settings.DEBUG:
                    send_mail(
                        'AIVIA - SyncU Email Verification OTP',
                        f'Your verification code is {otp_code}. It expires in 10 minutes.',
                        settings.DEFAULT_FROM_EMAIL,
                        [user.email],
                        fail_silently=False
                    )
                return Response(
                    {'detail': 'Email not verified. A new OTP has been sent.', 'is_active': False},
                    status=status.HTTP_403_FORBIDDEN
                )
            # proceed with normal login if verified and active
            if user.is_active and user.status == 'active':
                login(request, user)
                token, created = Token.objects.get_or_create(user=user)
                return Response({
                    'token': token.key,
                    'user': UserSerializer(user).data
                })
            return Response(
                {'detail': 'Account is inactive or suspended.'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        return Response(
            {'detail': 'Invalid credentials.'},
            status=status.HTTP_401_UNAUTHORIZED
        )


class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = RegisterSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            
            # Generate OTP for email verification
            otp_code = ''.join(random.choices(string.digits, k=6))
            expires_at = timezone.now() + timedelta(minutes=10)
            OTP.objects.create(
                user=user,
                code=otp_code,
                purpose='registration',
                expires_at=expires_at
            )
            
            if not settings.DEBUG:
                send_mail(
                    'AIVIA - SyncU Email Verification OTP',
                    f'Your verification code is {otp_code}. It expires in 10 minutes.',
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    fail_silently=False
                )
            
            response_data = {
                'detail': 'User registered successfully. Please verify your email.',
                'email': user.email,
            }
            if settings.DEBUG:
                response_data['otp'] = otp_code  # Include OTP in response only in debug mode
            
            return Response(response_data, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class VerifyOTPView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            otp = serializer.validated_data['otp_obj']
            
            # Activate user account and stamp verification time
            user.is_active = True
            user.email_verified_at = timezone.now()
            user.save()
            
            # Mark OTP as used
            otp.is_used = True
            otp.save()
            
            # Create auth token
            token, created = Token.objects.get_or_create(user=user)
            
            return Response({
                'detail': 'Email verified successfully.',
                'token': token.key,
                'user': UserSerializer(user).data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ForgotPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = ForgotPasswordSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            user = User.objects.get(email=email)
            
            # Generate OTP for password reset
            otp_code = ''.join(random.choices(string.digits, k=6))
            expires_at = timezone.now() + timedelta(minutes=10)
            OTP.objects.create(
                user=user,
                code=otp_code,
                purpose='password_reset',
                expires_at=expires_at
            )
            
            if not settings.DEBUG:
                send_mail(
                    'Password Reset OTP',
                    f'Your password reset code is {otp_code}. It expires in 10 minutes.',
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    fail_silently=False
                )
            
            response_data = {
                'detail': 'Password reset OTP sent to your email.',
                'email': user.email,
            }
            if settings.DEBUG:
                response_data['otp'] = otp_code  # Include OTP in response only in debug mode
            
            return Response(response_data, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = ResetPasswordSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            otp = serializer.validated_data['otp_obj']
            
            # Update password
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            
            # Mark OTP as used
            otp.is_used = True
            otp.save()
            
            return Response({
                'detail': 'Password reset successfully.'
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class GoogleLogin(SocialLoginView):
    adapter_class = GoogleOAuth2Adapter
    callback_url = settings.GOOGLE_CALLBACK_URL
    client_class = OAuth2Client


class LogoutView(APIView):
    def post(self, request):
        logout(request)
        return Response({'detail': 'Successfully logged out.'}, status=status.HTTP_200_OK)


class UserProfileView(generics.RetrieveUpdateAPIView):
    serializer_class = UserProfileSerializer
    
    def get_object(self):
        return self.request.user


class ChangePasswordView(APIView):
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        if serializer.is_valid():
            user = request.user
            old_password = serializer.validated_data['old_password']
            
            # Check if old password is correct
            if not user.check_password(old_password):
                return Response(
                    {'old_password': 'Wrong password.'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Set new password
            user.set_password(serializer.validated_data['new_password'])
            user.save()
            
            return Response({'detail': 'Password changed successfully.'}, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
