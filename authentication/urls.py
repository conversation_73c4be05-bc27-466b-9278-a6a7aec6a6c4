from django.urls import path
from rest_framework.authtoken.views import obtain_auth_token
from .views import (
    LoginView, RegisterView, VerifyOTPView, ForgotPasswordView,
    ResetPasswordView, GoogleLogin, LogoutView, UserProfileView,
    ChangePasswordView
)

urlpatterns = [
    path('login', LoginView.as_view(), name='login'),
    path('register', RegisterView.as_view(), name='register'),
    path('verify-otp', VerifyOTPView.as_view(), name='verify-otp'),
    path('forgot-password', ForgotPasswordView.as_view(), name='forgot-password'),
    path('reset-password', ResetPasswordView.as_view(), name='reset-password'),
    path('google-login', GoogleLogin.as_view(), name='google-login'),
    path('logout', LogoutView.as_view(), name='logout'),
    path('profile', UserProfileView.as_view(), name='profile'),
    path('change-password', ChangePasswordView.as_view(), name='change-password'),
]