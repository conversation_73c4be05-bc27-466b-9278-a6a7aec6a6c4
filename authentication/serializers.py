from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import timedelta
import random
from .models import OTP, User

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'name', 'mobile', 'status', 'is_active', 'date_joined')
        read_only_fields = ('id', 'date_joined', 'status')


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'name', 'mobile')
        read_only_fields = ('id', 'email')


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)

    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": "Passwords don't match."})
        
        try:
            validate_password(data['new_password'])
        except ValidationError as e:
            raise serializers.ValidationError({"new_password": list(e.messages)})
        
        return data


class RegisterSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True)
    confirm_password = serializers.CharField(write_only=True, required=True)

    class Meta:
        model = User
        fields = ('email', 'name', 'password', 'confirm_password', 'mobile')

    def validate(self, data):
        if data['password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": "Passwords don't match."})
        
        try:
            validate_password(data['password'])
        except ValidationError as e:
            raise serializers.ValidationError({"password": list(e.messages)})
        
        return data

    def create(self, validated_data):
        validated_data.pop('confirm_password')
        user = User.objects.create(
            email=validated_data['email'],
            name=validated_data['name'],
            mobile=validated_data.get('mobile', '')
        )
        user.set_password(validated_data['password'])
        user.is_active = False  # User will be activated after email verification
        user.save()
        return user


class OTPVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    otp = serializers.CharField(required=True, max_length=6)
    
    def validate(self, data):
        try:
            user = User.objects.get(email=data['email'])
            otp = OTP.objects.filter(
                user=user,
                code=data['otp'],
                purpose='registration',
                is_used=False,
                expires_at__gt=timezone.now()
            ).first()
            
            if not otp:
                raise serializers.ValidationError({"otp": "Invalid or expired OTP"})
            
            data['user'] = user
            data['otp_obj'] = otp
            return data
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": "User with this email does not exist"})


class ForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    
    def validate_email(self, value):
        try:
            User.objects.get(email=value)
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this email does not exist")


class ResetPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    otp = serializers.CharField(required=True, max_length=6)
    new_password = serializers.CharField(required=True)
    confirm_password = serializers.CharField(required=True)
    
    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": "Passwords don't match."})
        
        try:
            validate_password(data['new_password'])
        except ValidationError as e:
            raise serializers.ValidationError({"new_password": list(e.messages)})
        
        try:
            user = User.objects.get(email=data['email'])
            otp = OTP.objects.filter(
                user=user,
                code=data['otp'],
                purpose='password_reset',
                is_used=False,
                expires_at__gt=timezone.now()
            ).first()
            
            if not otp:
                raise serializers.ValidationError({"otp": "Invalid or expired OTP"})
            
            data['user'] = user
            data['otp_obj'] = otp
            return data
        except User.DoesNotExist:
            raise serializers.ValidationError({"email": "User with this email does not exist"})


class GoogleLoginSerializer(serializers.Serializer):
    id_token = serializers.CharField(required=True)