"""
N8N caller consumer - handles N8N API calls
"""
import logging
import requests
from typing import Dict, Any
from django.db import transaction

from .base import BaseEventConsumer
from ..kafka_config import KafkaConfig
from ..producer import event_publisher

logger = logging.getLogger(__name__)


class N8NCallerConsumer(BaseEventConsumer):
    """
    Handles N8N stage request events and makes actual API calls
    """
    
    def __init__(self):
        topics = [KafkaConfig.TOPICS['N8N_INTEGRATION_EVENTS']]
        super().__init__(
            group_id='n8n-caller',
            topics=topics
        )
    
    def handle_event(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
        """Handle N8N integration events"""
        try:
            event_type = headers.get('event_type')
            
            if event_type == 'N8NStageRequested':
                return self._handle_n8n_stage_request(event_data)
            else:
                logger.debug(f"Ignoring event type: {event_type}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling N8N event: {e}")
            return False
    
    def _handle_n8n_stage_request(self, event_data: Dict[str, Any]) -> bool:
        """Handle N8N stage request and make API call"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            n8n_url = event_data['n8n_url']
            payload = event_data['payload']
            
            logger.info(f"Making N8N API call for stage {stage}, video {video_id}")
            
            # Make the API call with retry logic
            success, response_data, error_message = self._call_n8n_api(
                n8n_url, payload, stage, video_id
            )
            
            if success:
                # Update video with execution ID if provided
                execution_id = response_data.get('execution_id')
                if execution_id:
                    with transaction.atomic():
                        from videos.models import Video
                        video = Video.objects.select_for_update().get(id=video_id)
                        video.latest_execution_id = execution_id
                        video.save(update_fields=['latest_execution_id'])
                
                logger.info(f"N8N API call successful for stage {stage}, video {video_id}")
                
                # Log the successful call
                from ..models import VideoEventLog
                VideoEventLog.objects.create(
                    video_id=video_id,
                    event_type='N8NStageRequested',
                    stage=stage,
                    correlation_id=correlation_id,
                    event_data={
                        'n8n_url': n8n_url,
                        'response_data': response_data,
                        'execution_id': execution_id
                    },
                    status='success'
                )
                
                return True
                
            else:
                # API call failed
                logger.error(f"N8N API call failed for stage {stage}, video {video_id}: {error_message}")
                
                # Publish N8N call failed event
                from ..schemas import EventFactory
                failed_event = EventFactory.create_n8n_stage_requested(
                    video_id=video_id,
                    stage=stage,
                    correlation_id=correlation_id,
                    n8n_url=n8n_url,
                    webhook_url=event_data.get('webhook_url', ''),
                    payload=payload
                )
                failed_event.event_type = 'N8NCallFailed'
                failed_event.error_message = error_message
                
                from ..producer import EventProducer
                producer = EventProducer()
                producer.publish_event(failed_event)
                
                # Also publish stage failed event
                event_publisher.publish_stage_failed(
                    video_id=video_id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=f"N8N API call failed: {error_message}"
                )
                
                # Log the failed call
                from ..models import VideoEventLog
                VideoEventLog.objects.create(
                    video_id=video_id,
                    event_type='N8NStageRequested',
                    stage=stage,
                    correlation_id=correlation_id,
                    event_data={
                        'n8n_url': n8n_url,
                        'error_message': error_message
                    },
                    status='failed'
                )
                
                return True  # Return True because we handled the error appropriately
                
        except Exception as e:
            logger.error(f"Error handling N8N stage request: {e}")
            return False
    
    def _call_n8n_api(self, url: str, payload: Dict[str, Any], stage: str, video_id: int, max_retries: int = 3):
        """
        Make N8N API call with retry logic
        
        Returns:
            tuple: (success: bool, response_data: dict, error_message: str)
        """
        last_error = None
        
        for attempt in range(max_retries):
            try:
                logger.info(f"N8N API call attempt {attempt + 1} for stage {stage}, video {video_id}")
                
                response = requests.post(
                    url,
                    json=payload,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                response.raise_for_status()
                response_data = response.json()
                
                # Check if we got the expected response format
                if response_data.get('status') == 'processing':
                    return True, response_data, None
                else:
                    error_msg = f"Unexpected N8N response: {response_data}"
                    logger.warning(error_msg)
                    last_error = error_msg
                    
            except requests.RequestException as e:
                error_msg = f"Network error: {str(e)}"
                logger.warning(f"N8N API call attempt {attempt + 1} failed: {error_msg}")
                last_error = error_msg
                
                # Wait before retry (exponential backoff)
                if attempt < max_retries - 1:
                    import time
                    wait_time = (2 ** attempt) * 1  # 1, 2, 4 seconds
                    time.sleep(wait_time)
                    
            except Exception as e:
                error_msg = f"Unexpected error: {str(e)}"
                logger.error(f"N8N API call attempt {attempt + 1} failed: {error_msg}")
                last_error = error_msg
                break  # Don't retry on unexpected errors
        
        return False, {}, last_error or "N8N API call failed after all retries"
