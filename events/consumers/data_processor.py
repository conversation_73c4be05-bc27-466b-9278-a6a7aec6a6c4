"""
Data processor consumer - handles data updates and validation
"""
import logging
from typing import Dict, Any
from django.db import transaction

from .base import BaseEventConsumer
from ..kafka_config import KafkaConfig
from ..producer import event_publisher

logger = logging.getLogger(__name__)


class DataProcessorConsumer(BaseEventConsumer):
    """
    Handles callback events and updates video data
    """
    
    def __init__(self):
        topics = [KafkaConfig.TOPICS['N8N_INTEGRATION_EVENTS']]
        super().__init__(
            group_id='data-processor',
            topics=topics
        )
    
    def handle_event(self, event_data: Dict[str, Any], headers: Dict[str, str]) -> bool:
        """Handle N8N callback events"""
        try:
            event_type = headers.get('event_type')
            
            if event_type == 'N8NCallbackReceived':
                return self._handle_callback_received(event_data)
            else:
                logger.debug(f"Ignoring event type: {event_type}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling data processor event: {e}")
            return False
    
    def _handle_callback_received(self, event_data: Dict[str, Any]) -> bool:
        """Handle N8N callback and update video data"""
        try:
            video_id = event_data['video_id']
            stage = event_data['stage']
            correlation_id = event_data['correlation_id']
            response_data = event_data.get('response_data', {})
            
            logger.info(f"Processing callback for stage {stage}, video {video_id}")
            
            # Validate response data
            if not self._validate_response_data(stage, response_data):
                error_msg = f"Invalid response data for stage {stage}"
                logger.error(error_msg)
                
                # Publish stage failed event
                event_publisher.publish_stage_failed(
                    video_id=video_id,
                    stage=stage,
                    correlation_id=correlation_id,
                    error_message=error_msg
                )
                return True
            
            # Update video with stage-specific data
            with transaction.atomic():
                from videos.models import Video
                
                video = Video.objects.select_for_update().get(id=video_id)
                
                # Process stage-specific data
                self._update_video_with_stage_data(video, stage, response_data)
                
                # Save the video
                video.save()
                
                # Log the successful processing
                from ..models import VideoEventLog
                VideoEventLog.objects.create(
                    video_id=video_id,
                    event_type='N8NCallbackReceived',
                    stage=stage,
                    correlation_id=correlation_id,
                    event_data=response_data,
                    status='processed'
                )
            
            # Publish stage completed event
            success = event_publisher.publish_stage_completed(
                video_id=video_id,
                stage=stage,
                correlation_id=correlation_id,
                stage_data=response_data
            )
            
            if not success:
                logger.error(f"Failed to publish StageCompleted event for stage {stage}")
                return False
            
            logger.info(f"Successfully processed callback for stage {stage}, video {video_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing callback: {e}")
            return False
    
    def _validate_response_data(self, stage: str, response_data: Dict[str, Any]) -> bool:
        """Validate response data for a specific stage"""
        try:
            # Define required fields for each stage
            required_fields = {
                'script_generation': ['script'],
                'voice_generation': ['speech_url'],
                'caption_generation': ['captions'],
                'image_prompt_generation': ['image_prompts'],
                'image_generation': ['generated_images'],
                'clip_creation': ['clips'],
                'track_creation': ['tracks'],
                'video_composition': ['video_url']
            }
            
            stage_required = required_fields.get(stage, [])
            
            for field in stage_required:
                if field not in response_data or not response_data[field]:
                    logger.warning(f"Missing required field '{field}' for stage '{stage}'")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating response data: {e}")
            return False
    
    def _update_video_with_stage_data(self, video, stage: str, data: Dict[str, Any]):
        """Update video instance with stage-specific data"""
        try:
            # Use the existing stage data processor
            from videos.services.stage_data_processor import STAGE_PROCESSORS
            
            processor = STAGE_PROCESSORS.get(stage)
            if processor:
                processor(video, data)
                logger.debug(f"Updated video {video.id} with {stage} data using processor")
            else:
                logger.warning(f"No processor found for stage: {stage}")
                # Fallback: store data in a generic field if available
                if hasattr(video, 'stage_data'):
                    if not video.stage_data:
                        video.stage_data = {}
                    video.stage_data[stage] = data
                    
        except Exception as e:
            logger.error(f"Error updating video with stage data: {e}")
            raise
