"""
Stage initiation handler - bridges consumers and services
"""
import logging
from typing import Dict, Any
from django.db import transaction

from videos.models import Video
from videos.services.video_creation_service import video_creation_service
from events.producer import event_publisher

logger = logging.getLogger(__name__)


class StageInitiationHandler:
    """Handles stage initiation events by calling the video creation service"""
    
    @staticmethod
    def handle_stage_initiation(video_id: int, stage: str, correlation_id: str) -> bool:
        """
        Handle stage initiation event
        
        Args:
            video_id: ID of the video
            stage: Stage to initiate
            correlation_id: Correlation ID for tracking
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            with transaction.atomic():
                video = Video.objects.select_for_update().get(id=video_id)
                
                # Call the event-driven service to handle stage initiation
                success = video_creation_service.handle_stage_initiation(
                    video=video,
                    stage=stage,
                    correlation_id=correlation_id
                )
                
                if not success:
                    logger.error(f"Failed to initiate stage {stage} for video {video_id}")
                    
                    # Publish stage failed event
                    event_publisher.publish_stage_failed(
                        video_id=video_id,
                        stage=stage,
                        correlation_id=correlation_id,
                        error_message=f"Failed to initiate stage {stage}"
                    )
                    
                    return False
                
                logger.info(f"Successfully initiated stage {stage} for video {video_id}")
                return True
                
        except Video.DoesNotExist:
            logger.error(f"Video {video_id} not found for stage initiation")
            return False
        except Exception as e:
            logger.error(f"Error handling stage initiation for video {video_id}, stage {stage}: {e}")
            return False
