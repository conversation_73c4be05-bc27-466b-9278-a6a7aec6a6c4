# Generated by Django 5.2.1 on 2025-08-20 12:25

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EventStore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_id', models.UUIDField(db_index=True, unique=True)),
                ('event_type', models.CharField(db_index=True, max_length=100)),
                ('video_id', models.IntegerField(db_index=True)),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('event_data', models.J<PERSON>NField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('published', 'Published'), ('failed', 'Failed')], db_index=True, default='pending', max_length=20)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True, db_index=True)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('error_message', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'event_store',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='RetryPolicy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(db_index=True, max_length=100)),
                ('stage', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('max_retries', models.IntegerField(default=3)),
                ('initial_delay_seconds', models.IntegerField(default=60)),
                ('backoff_multiplier', models.FloatField(default=2.0)),
                ('max_delay_seconds', models.IntegerField(default=3600)),
                ('is_active', models.BooleanField(db_index=True, default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'retry_policy',
            },
        ),
        migrations.CreateModel(
            name='VideoEventLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('video_id', models.IntegerField(db_index=True)),
                ('event_type', models.CharField(db_index=True, max_length=100)),
                ('stage', models.CharField(blank=True, db_index=True, max_length=50, null=True)),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('event_data', models.JSONField(default=dict)),
                ('status', models.CharField(db_index=True, max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('processing_duration_ms', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'video_event_log',
                'ordering': ['created_at'],
            },
        ),
    ]
