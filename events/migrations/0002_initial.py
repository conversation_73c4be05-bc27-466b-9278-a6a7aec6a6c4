# Generated by Django 5.2.1 on 2025-08-20 12:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('events', '0001_initial'),
        ('videos', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='WorkflowState',
            fields=[
                ('video_id', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, related_name='workflow_state', serialize=False, to='videos.video')),
                ('correlation_id', models.CharField(db_index=True, max_length=100)),
                ('current_stage', models.CharField(db_index=True, max_length=50)),
                ('stages_completed', models.JSONField(default=list)),
                ('stages_failed', models.J<PERSON><PERSON>ield(default=list)),
                ('retry_counts', models.<PERSON><PERSON><PERSON><PERSON>(default=dict)),
                ('is_paused', models.Boolean<PERSON>ield(db_index=True, default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_activity_at', models.DateTimeField(auto_now=True, db_index=True)),
            ],
            options={
                'db_table': 'workflow_state',
            },
        ),
        migrations.AddIndex(
            model_name='eventstore',
            index=models.Index(fields=['status', 'created_at'], name='event_store_status_0fada7_idx'),
        ),
        migrations.AddIndex(
            model_name='eventstore',
            index=models.Index(fields=['video_id', 'event_type'], name='event_store_video_i_2e3fff_idx'),
        ),
        migrations.AddIndex(
            model_name='eventstore',
            index=models.Index(fields=['correlation_id', 'created_at'], name='event_store_correla_c20f48_idx'),
        ),
        migrations.AddIndex(
            model_name='retrypolicy',
            index=models.Index(fields=['event_type', 'is_active'], name='retry_polic_event_t_1d2e4a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='retrypolicy',
            unique_together={('event_type', 'stage')},
        ),
        migrations.AddIndex(
            model_name='videoeventlog',
            index=models.Index(fields=['video_id', 'created_at'], name='video_event_video_i_b910ba_idx'),
        ),
        migrations.AddIndex(
            model_name='videoeventlog',
            index=models.Index(fields=['correlation_id', 'created_at'], name='video_event_correla_2a221f_idx'),
        ),
        migrations.AddIndex(
            model_name='videoeventlog',
            index=models.Index(fields=['stage', 'status'], name='video_event_stage_7ae331_idx'),
        ),
        migrations.AddIndex(
            model_name='workflowstate',
            index=models.Index(fields=['current_stage', 'is_paused'], name='workflow_st_current_55a4c9_idx'),
        ),
        migrations.AddIndex(
            model_name='workflowstate',
            index=models.Index(fields=['last_activity_at'], name='workflow_st_last_ac_05d55c_idx'),
        ),
    ]
