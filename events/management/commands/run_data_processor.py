"""
Django management command to run Data Processor consumer
"""
from django.core.management.base import BaseCommand
from events.consumers.data_processor import DataProcessorConsumer


class Command(BaseCommand):
    help = 'Run the Data Processor consumer'

    def handle(self, *args, **options):
        self.stdout.write('Starting Data Processor consumer...')
        
        consumer = DataProcessorConsumer()
        try:
            consumer.start()
        except KeyboardInterrupt:
            self.stdout.write('\nStopping Data Processor consumer...')
        finally:
            consumer.shutdown()
            self.stdout.write(
                self.style.SUCCESS('Data Processor consumer stopped')
            )
