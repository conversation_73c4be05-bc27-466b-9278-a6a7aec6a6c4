"""
Django management command to run N8N Caller consumer
"""
from django.core.management.base import BaseCommand
from events.consumers.n8n_caller import N8NCallerConsumer


class Command(BaseCommand):
    help = 'Run the N8N Caller consumer'

    def handle(self, *args, **options):
        self.stdout.write('Starting N8N Caller consumer...')
        
        consumer = N8NCallerConsumer()
        try:
            consumer.start()
        except KeyboardInterrupt:
            self.stdout.write('\nStopping N8N Caller consumer...')
        finally:
            consumer.shutdown()
            self.stdout.write(
                self.style.SUCCESS('N8N Caller consumer stopped')
            )
