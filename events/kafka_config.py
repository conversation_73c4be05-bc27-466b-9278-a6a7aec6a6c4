"""
Kafka configuration and topic management
"""
import logging
from typing import Dict, List
from django.conf import settings
from confluent_kafka import Producer, Consumer, KafkaError
from confluent_kafka.admin import AdminClient, NewTopic, ConfigResource, ResourceType
import json

logger = logging.getLogger(__name__)


class KafkaConfig:
    """Kafka configuration management"""
    
    # Topic definitions
    TOPICS = {
        'VIDEO_CREATION_EVENTS': 'video-creation-events',
        'STAGE_EVENTS': 'stage-events', 
        'N8N_INTEGRATION_EVENTS': 'n8n-integration-events',
        'ERROR_EVENTS': 'error-events',
        'RETRY_EVENTS': 'retry-events',
        'DLQ_EVENTS': 'dlq-events'
    }
    
    # Topic configurations
    TOPIC_CONFIGS = {
        'video-creation-events': {
            'num_partitions': 3,
            'replication_factor': 1,
            'config': {
                'retention.ms': '604800000',  # 7 days
                'cleanup.policy': 'delete'
            }
        },
        'stage-events': {
            'num_partitions': 6,
            'replication_factor': 1,
            'config': {
                'retention.ms': '604800000',  # 7 days
                'cleanup.policy': 'delete'
            }
        },
        'n8n-integration-events': {
            'num_partitions': 3,
            'replication_factor': 1,
            'config': {
                'retention.ms': '259200000',  # 3 days
                'cleanup.policy': 'delete'
            }
        },
        'error-events': {
            'num_partitions': 2,
            'replication_factor': 1,
            'config': {
                'retention.ms': '2592000000',  # 30 days
                'cleanup.policy': 'delete'
            }
        },
        'retry-events': {
            'num_partitions': 2,
            'replication_factor': 1,
            'config': {
                'retention.ms': '86400000',  # 1 day
                'cleanup.policy': 'delete'
            }
        },
        'dlq-events': {
            'num_partitions': 1,
            'replication_factor': 1,
            'config': {
                'retention.ms': '2592000000',  # 30 days
                'cleanup.policy': 'delete'
            }
        }
    }
    
    @classmethod
    def get_producer_config(cls) -> Dict:
        """Get Kafka producer configuration"""
        return {
            'bootstrap.servers': getattr(settings, 'KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092'),
            'client.id': f'video-creation-producer-{settings.SECRET_KEY[:8]}',
            'acks': 'all',
            'retries': 3,
            'max.in.flight.requests.per.connection': 1,
            'enable.idempotence': True,
            'compression.type': 'snappy',
            'batch.size': 16384,
            'linger.ms': 10,
        }
    
    @classmethod
    def get_consumer_config(cls, group_id: str) -> Dict:
        """Get Kafka consumer configuration"""
        return {
            'bootstrap.servers': getattr(settings, 'KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092'),
            'group.id': group_id,
            'client.id': f'video-creation-consumer-{group_id}-{settings.SECRET_KEY[:8]}',
            'auto.offset.reset': 'earliest',
            'enable.auto.commit': False,
            'max.poll.interval.ms': 300000,  # 5 minutes
            'session.timeout.ms': 30000,
            'heartbeat.interval.ms': 10000,
            'fetch.min.bytes': 1,
        }
    
    @classmethod
    def get_admin_config(cls) -> Dict:
        """Get Kafka admin configuration"""
        return {
            'bootstrap.servers': getattr(settings, 'KAFKA_BOOTSTRAP_SERVERS', 'localhost:9092'),
            'client.id': f'video-creation-admin-{settings.SECRET_KEY[:8]}',
        }


class KafkaTopicManager:
    """Manages Kafka topics creation and configuration"""
    
    def __init__(self):
        self.admin_client = AdminClient(KafkaConfig.get_admin_config())
    
    def create_topics(self) -> bool:
        """Create all required topics"""
        try:
            # Get existing topics
            existing_topics = set(self.admin_client.list_topics().topics.keys())
            
            # Create new topics
            new_topics = []
            for topic_name, topic_config in KafkaConfig.TOPIC_CONFIGS.items():
                if topic_name not in existing_topics:
                    new_topic = NewTopic(
                        topic=topic_name,
                        num_partitions=topic_config['num_partitions'],
                        replication_factor=topic_config['replication_factor'],
                        config=topic_config['config']
                    )
                    new_topics.append(new_topic)
            
            if new_topics:
                # Create topics
                futures = self.admin_client.create_topics(new_topics)
                
                # Wait for creation to complete
                for topic, future in futures.items():
                    try:
                        future.result()  # The result itself is None
                        logger.info(f"Topic {topic} created successfully")
                    except Exception as e:
                        logger.error(f"Failed to create topic {topic}: {e}")
                        return False
                
                logger.info(f"Created {len(new_topics)} new topics")
            else:
                logger.info("All topics already exist")
            
            return True
            
        except Exception as e:
            logger.error(f"Error creating topics: {e}")
            return False
    
    def delete_topics(self, topic_names: List[str]) -> bool:
        """Delete specified topics (for development/testing)"""
        try:
            futures = self.admin_client.delete_topics(topic_names)
            
            for topic, future in futures.items():
                try:
                    future.result()
                    logger.info(f"Topic {topic} deleted successfully")
                except Exception as e:
                    logger.error(f"Failed to delete topic {topic}: {e}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error deleting topics: {e}")
            return False
    
    def topic_exists(self, topic_name: str) -> bool:
        """Check if a topic exists"""
        try:
            topics = self.admin_client.list_topics().topics
            return topic_name in topics
        except Exception as e:
            logger.error(f"Error checking topic existence: {e}")
            return False


def get_topic_for_event_type(event_type: str) -> str:
    """Map event type to appropriate Kafka topic"""
    mapping = {
        'VideoCreationStarted': KafkaConfig.TOPICS['VIDEO_CREATION_EVENTS'],
        'VideoCreationCompleted': KafkaConfig.TOPICS['VIDEO_CREATION_EVENTS'],
        'VideoCreationFailed': KafkaConfig.TOPICS['VIDEO_CREATION_EVENTS'],
        
        'StageInitiated': KafkaConfig.TOPICS['STAGE_EVENTS'],
        'StageCompleted': KafkaConfig.TOPICS['STAGE_EVENTS'],
        'StageFailed': KafkaConfig.TOPICS['STAGE_EVENTS'],
        'StageSkipped': KafkaConfig.TOPICS['STAGE_EVENTS'],
        
        'N8NStageRequested': KafkaConfig.TOPICS['N8N_INTEGRATION_EVENTS'],
        'N8NCallbackReceived': KafkaConfig.TOPICS['N8N_INTEGRATION_EVENTS'],
        'N8NCallFailed': KafkaConfig.TOPICS['N8N_INTEGRATION_EVENTS'],
        
        'RetryStageRequested': KafkaConfig.TOPICS['RETRY_EVENTS'],
        'WorkflowPaused': KafkaConfig.TOPICS['VIDEO_CREATION_EVENTS'],
        'WorkflowResumed': KafkaConfig.TOPICS['VIDEO_CREATION_EVENTS'],
    }
    
    return mapping.get(event_type, KafkaConfig.TOPICS['ERROR_EVENTS'])


def get_partition_key(video_id: int) -> str:
    """Generate partition key for video events to ensure ordering"""
    return str(video_id)
